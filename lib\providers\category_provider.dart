import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/category.dart';
import '../models/event_workspace.dart';
import '../repositories/category_repository.dart';
import '../providers/data_sync_provider.dart';
import '../services/realtime_sync_service_main.dart';
import '../providers/realtime_sync_provider.dart';
import '../providers/unified_workspace_provider.dart';
import '../utils/logger_utils.dart';
import 'subscription_provider.dart';
import '../models/subscription_plan.dart';

/// 카테고리 관련 액션을 처리하는 Provider
class CategoryNotifier extends StateNotifier<AsyncValue<List<Category>>> {
  final CategoryRepository _repository;
  final Ref _ref;
  StreamSubscription<RealtimeDataChange>? _realtimeSubscription;
  Timer? _debounceTimer;

  // 실시간 동기화 보호장치
  final Set<int> _recentlyAddedCategories = {}; // 최근 추가한 카테고리 ID 캐시 (무한 루프 방지)
  final Set<int> _recentlyUpdatedCategories = {}; // 최근 수정한 카테고리 ID 캐시 (무한 루프 방지)
  final Set<int> _recentlyDeletedCategories = {}; // 최근 삭제한 카테고리 ID 캐시 (무한 루프 방지)
  bool _isUploading = false; // 업로드 중 상태 관리

  CategoryNotifier(this._repository, this._ref) : super(const AsyncValue.loading()) {
    // 생성자에서 바로 loadCategories를 호출하지 않음
    // 대신 필요할 때 명시적으로 호출
    _watchCurrentEvent();
    _setupRealtimeSync();
  }

  /// 카테고리 목록 로드 (깜빡거림 방지를 위해 기존 데이터 유지)
  Future<void> loadCategories({int? eventId}) async {
    try {
      // 카테고리는 모든 플랜에서 로컬 사용 가능 (서버 동기화만 프로 플랜 제한)

      // 기존 데이터가 있으면 loading 상태로 변경하지 않음 (깜빡거림 방지)
      final hasExistingData = state.hasValue && state.value!.isNotEmpty;
      if (!hasExistingData) {
        state = const AsyncValue.loading();
      }

      await _repository.loadCategories(eventId: eventId);
      final categories = _repository.state.categories;
      state = AsyncValue.data(categories);
    } catch (e) {
      LoggerUtils.logError('카테고리 로딩 실패', error: e, tag: 'CategoryNotifier');
      state = AsyncValue.error(e, StackTrace.current);
    } finally {
      // 안전장치: 로딩 상태가 계속 유지되지 않도록 보장
      if (state.isLoading) {
        // 로딩 상태가 계속 유지되면 빈 데이터로라도 완료 처리
        state = const AsyncValue.data([]);
      }
    }
  }

  /// 카테고리 추가 (로컬 DB + Firebase 동기화 포함)
  Future<bool> addCategory({
    required String name,
    int? eventId,
    int? sortOrder,
    int? color,
  }) async {
    try {
      // 업로드 중 상태 확인
      if (_isUploading) {
        LoggerUtils.logWarning('카테고리 업로드 중이므로 새 추가 요청 무시', tag: 'CategoryNotifier');
        return false;
      }

      _isUploading = true;

      final result = await _repository.addCategory(
        name: name,
        eventId: eventId,
        sortOrder: sortOrder,
        color: color,
      );

      if (result != null) {
        // 최근 추가한 카테고리로 캐시 (무한 루프 방지용)
        if (result.id != null) {
          _recentlyAddedCategories.add(result.id!);
          // 5초 후 캐시에서 제거
          Future.delayed(const Duration(seconds: 5), () {
            _recentlyAddedCategories.remove(result.id!);
          });
        }

        // 성공시 목록 새로고침
        await loadCategories(eventId: eventId);

        // 실시간 동기화: 새 카테고리를 Firebase에 업로드
        try {
          final dataSyncService = _ref.read(dataSyncServiceProvider);
          await dataSyncService.uploadSingleCategory(result);
          LoggerUtils.logInfo('카테고리 실시간 동기화 완료: ${result.name}', tag: 'CategoryNotifier');
        } catch (e) {
          LoggerUtils.logWarning('카테고리 실시간 동기화 실패: ${result.name}', tag: 'CategoryNotifier', error: e);
          // 로컬 저장은 성공했으므로 true 반환
        }

        return true;
      }
      return false;
    } catch (e) {
      LoggerUtils.logError('Failed to add category', error: e, tag: 'CategoryNotifier');
      return false;
    } finally {
      _isUploading = false;
    }
  }



  /// 카테고리 수정 (로컬 DB + Firebase 동기화 포함)
  Future<bool> updateCategory({
    required int id,
    String? name,
    int? sortOrder,
    int? color,
  }) async {
    try {
      // 업로드 중 상태 확인
      if (_isUploading) {
        LoggerUtils.logWarning('카테고리 업로드 중이므로 수정 요청 무시', tag: 'CategoryNotifier');
        return false;
      }

      _isUploading = true;

      // 수정 전 카테고리 정보 가져오기 (실시간 동기화용)
      final originalCategory = await _repository.getCategoryById(id);
      
      final result = await _repository.updateCategory(
        id: id,
        name: name,
        sortOrder: sortOrder,
        color: color,
      );
      
      if (result && originalCategory != null) {
        // 최근 수정한 카테고리로 캐시 (무한 루프 방지용)
        _recentlyUpdatedCategories.add(id);
        // 5초 후 캐시에서 제거
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyUpdatedCategories.remove(id);
        });

        // 성공시 목록 새로고침
        await loadCategories();

        // 실시간 동기화: 수정된 카테고리를 Firebase에 업로드
        try {
          // 수정된 정보로 카테고리 생성
          final updatedCategory = originalCategory.copyWith(
            name: name ?? originalCategory.name,
            sortOrder: sortOrder ?? originalCategory.sortOrder,
            color: color ?? originalCategory.color,
          );
          final dataSyncService = _ref.read(dataSyncServiceProvider);
          await dataSyncService.uploadSingleCategory(updatedCategory);
          LoggerUtils.logInfo('카테고리 수정 실시간 동기화 완료: ${updatedCategory.name}', tag: 'CategoryNotifier');
        } catch (e) {
          LoggerUtils.logWarning('카테고리 수정 실시간 동기화 실패: ID $id', tag: 'CategoryNotifier', error: e);
          // 로컬 저장은 성공했으므로 true 반환
        }

        return true;
      }
      return false;
    } catch (e) {
      LoggerUtils.logError('Failed to update category', error: e, tag: 'CategoryNotifier');
      return false;
    } finally {
      _isUploading = false;
    }
  }



  /// 카테고리 삭제
  Future<bool> deleteCategory(int id) async {
    try {
      // 업로드 중 상태 확인
      if (_isUploading) {
        LoggerUtils.logWarning('카테고리 업로드 중이므로 삭제 요청 무시', tag: 'CategoryNotifier');
        return false;
      }

      _isUploading = true;

      // 삭제 전 카테고리 정보 가져오기 (실시간 동기화용)
      final categoryToDelete = await _repository.getCategoryById(id);
      
      final result = await _repository.deleteCategory(id);
      
      if (result && categoryToDelete != null) {
        // 최근 삭제한 카테고리로 캐시 (무한 루프 방지용)
        _recentlyDeletedCategories.add(id);
        // 5초 후 캐시에서 제거
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyDeletedCategories.remove(id);
        });

        // 성공시 목록 새로고침
        await loadCategories();

        // 실시간 동기화: Firebase에서 카테고리 삭제
        try {
          final dataSyncService = _ref.read(dataSyncServiceProvider);
          await dataSyncService.deleteSingleCategory(categoryToDelete);
          LoggerUtils.logInfo('카테고리 삭제 실시간 동기화 완료: ${categoryToDelete.name}', tag: 'CategoryNotifier');
        } catch (e) {
          LoggerUtils.logWarning('카테고리 삭제 실시간 동기화 실패: ${categoryToDelete.name}', tag: 'CategoryNotifier', error: e);
          // 로컬 삭제는 성공했으므로 true 반환
        }

        return true;
      }
      return false;
    } catch (e) {
      LoggerUtils.logError('Failed to delete category', error: e, tag: 'CategoryNotifier');
      return false;
    } finally {
      _isUploading = false;
    }
  }

  /// 카테고리 순서 재정렬
  Future<bool> reorderCategories(List<Category> reorderedCategories) async {
    try {
      final result = await _repository.reorderCategories(reorderedCategories);

      if (result) {
        // 성공시 목록 새로고침
        await loadCategories();

        // 실시간 동기화: 순서가 변경된 모든 카테고리를 Firebase에 업로드
        try {
          final dataSyncService = _ref.read(dataSyncServiceProvider);

          // 업데이트된 sortOrder를 가진 카테고리 객체들 생성
          for (int i = 0; i < reorderedCategories.length; i++) {
            final updatedCategory = reorderedCategories[i].copyWith(sortOrder: i);
            await dataSyncService.uploadSingleCategory(updatedCategory);
          }

          LoggerUtils.logInfo('카테고리 순서 변경 실시간 동기화 완료: ${reorderedCategories.length}개', tag: 'CategoryNotifier');
        } catch (e) {
          LoggerUtils.logWarning('카테고리 순서 변경 실시간 동기화 실패', tag: 'CategoryNotifier', error: e);
          // 로컬 저장은 성공했으므로 true 반환
        }

        return true;
      }
      return false;
    } catch (e) {
      LoggerUtils.logError('Failed to reorder categories', error: e, tag: 'CategoryNotifier');
      return false;
    }
  }

  /// 특정 카테고리 조회
  Future<Category?> getCategoryById(int id) async {
    try {
      return await _repository.getCategoryById(id);
    } catch (e) {
      LoggerUtils.logError('Failed to get category by id', error: e, tag: 'CategoryNotifier');
      return null;
    }
  }

  /// 이벤트에 기본 카테고리 생성
  Future<void> createDefaultCategoryForEvent(int eventId) async {
    try {
      await _repository.createDefaultCategoryForEvent(eventId);
      await loadCategories(eventId: eventId);
    } catch (e) {
      LoggerUtils.logError('Failed to create default category', error: e, tag: 'CategoryNotifier');
    }
  }

  /// 현재 행사 워크스페이스 변경 감지 및 자동 새로고침
  void _watchCurrentEvent() {
    _ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) {
      if (previous?.id != next?.id) {
        LoggerUtils.logInfo('현재 행사 워크스페이스 변경 감지 - CategoryNotifier 새로고침: ${previous?.name} -> ${next?.name}', tag: 'CategoryNotifier');

        if (next != null) {
          // 이전 데이터를 유지하면서 새 데이터 로딩 (깜빡거림 방지)
          // state = const AsyncValue.loading(); // 이 줄을 제거하여 깜빡거림 방지

          // 즉시 카테고리 로딩 (지연 제거)
          loadCategories(eventId: next.id);

          _setupRealtimeSync(); // 새 워크스페이스에 대한 실시간 동기화 재설정
        } else {
          // 현재 행사 워크스페이스가 null이 되면 카테고리 목록 클리어
          state = const AsyncValue.data([]);
        }
      }
    });
  }

  /// 실시간 동기화 설정
  void _setupRealtimeSync() {
    try {
      // 기존 구독 해제
      _realtimeSubscription?.cancel();

      final currentWorkspace = _ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 없어 실시간 동기화를 설정할 수 없습니다', tag: 'CategoryNotifier');
        return;
      }

      // 프로 플랜만 실시간 동기화 지원
      final subscriptionState = _ref.read(subscriptionNotifierProvider);
      final isProUser = subscriptionState.when(
        data: (planType) => planType == SubscriptionPlanType.pro,
        loading: () => false,
        error: (_, __) => false,
      );
      if (!isProUser) {
        LoggerUtils.logInfo('프로 플랜이 아니므로 카테고리 실시간 동기화를 건너뜁니다', tag: 'CategoryNotifier');
        return;
      }

      // 실시간 동기화 서비스 가져오기
      final realtimeService = _ref.read(realtimeSyncServiceProvider);

      LoggerUtils.logInfo('카테고리 실시간 데이터 스트림 구독 시작', tag: 'CategoryNotifier');

      // 데이터 변경 리스너 설정
      _realtimeSubscription = realtimeService.dataChanges.listen((change) {
        _handleRealtimeDataChange(change);
      });

      LoggerUtils.logInfo('CategoryNotifier 실시간 동기화 리스너 설정 완료', tag: 'CategoryNotifier');
    } catch (e) {
      LoggerUtils.logError('CategoryNotifier 실시간 동기화 설정 실패', tag: 'CategoryNotifier', error: e);
    }
  }

  /// 실시간 데이터 변경 처리
  void _handleRealtimeDataChange(RealtimeDataChange change) {
    try {
      final currentWorkspace = _ref.read(currentWorkspaceProvider);

      // 현재 워크스페이스의 카테고리 변경인지 확인
      if (currentWorkspace?.id == change.eventId && change.collectionName == 'categories') {
        final categoryId = int.tryParse(change.documentId);

        // 자기가 최근에 추가/수정/삭제한 카테고리는 무시 (무한 루프 방지)
        if (categoryId != null) {
          if (_recentlyAddedCategories.contains(categoryId)) {
            LoggerUtils.logDebug('최근 추가한 카테고리 무시: ID $categoryId', tag: 'CategoryNotifier');
            return;
          }
          if (_recentlyUpdatedCategories.contains(categoryId)) {
            LoggerUtils.logDebug('최근 수정한 카테고리 무시: ID $categoryId', tag: 'CategoryNotifier');
            return;
          }
          if (_recentlyDeletedCategories.contains(categoryId)) {
            LoggerUtils.logDebug('최근 삭제한 카테고리 무시: ID $categoryId', tag: 'CategoryNotifier');
            return;
          }
        }

        LoggerUtils.logInfo('카테고리 실시간 변경 감지: ${change.changeType.name} - ${change.documentId}', tag: 'CategoryNotifier');

        // 무한 갱신 방지: 디바운싱을 통해 연속된 변경사항을 한 번에 처리
        _debouncedSyncAndRefresh(currentWorkspace!.id);
      }
    } catch (e) {
      LoggerUtils.logError('실시간 데이터 변경 처리 실패', tag: 'CategoryNotifier', error: e);
    }
  }

  /// 디바운싱을 통한 카테고리 동기화 (무한 갱신 방지)
  void _debouncedSyncAndRefresh(int eventId) {
    // 기존 타이머 취소
    _debounceTimer?.cancel();

    // 500ms 후에 동기화 실행 (연속된 변경사항을 한 번에 처리)
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _syncAndRefreshCategories(eventId);
    });
  }

  /// Firebase에서 최신 카테고리 데이터를 다운로드하고 새로고침
  Future<void> _syncAndRefreshCategories(int eventId) async {
    try {
      LoggerUtils.logInfo('카테고리 실시간 동기화 시작: eventId $eventId', tag: 'CategoryNotifier');

      // 1. Firebase에서 최신 데이터 다운로드
      final dataSyncService = _ref.read(dataSyncServiceProvider);
      await dataSyncService.downloadCategoriesFromFirebase(eventId);

      // 2. 로컬 데이터 새로고침
      await loadCategories(eventId: eventId);

      LoggerUtils.logInfo('카테고리 실시간 동기화 완료: eventId $eventId', tag: 'CategoryNotifier');
    } catch (e) {
      LoggerUtils.logError('카테고리 실시간 동기화 실패: eventId $eventId', tag: 'CategoryNotifier', error: e);

      // 동기화 실패해도 로컬 데이터라도 새로고침 시도
      try {
        await loadCategories(eventId: eventId);
      } catch (loadError) {
        LoggerUtils.logError('카테고리 로컬 새로고침도 실패', tag: 'CategoryNotifier', error: loadError);
      }
    }
  }

  @override
  void dispose() {
    _realtimeSubscription?.cancel();
    _debounceTimer?.cancel();

    // 캐시 정리
    _recentlyAddedCategories.clear();
    _recentlyUpdatedCategories.clear();
    _recentlyDeletedCategories.clear();

    super.dispose();
  }

  /// 현재 에러 메시지 가져오기
  String? get errorMessage => _repository.state.error;

  /// 로딩 상태 확인
  bool get isLoading => _repository.state.isLoading;
}

/// 카테고리 Provider
final categoryNotifierProvider = StateNotifierProvider<CategoryNotifier, AsyncValue<List<Category>>>((ref) {
  final repository = ref.watch(categoryRepositoryProvider.notifier);
  return CategoryNotifier(repository, ref);
});

/// 현재 카테고리 목록을 제공하는 Provider
final currentCategoriesProvider = Provider<List<Category>>((ref) {
  final asyncCategories = ref.watch(categoryNotifierProvider);
  return asyncCategories.maybeWhen(
    data: (categories) => categories,
    orElse: () => [],
  );
});

/// 카테고리 로딩 상태를 제공하는 Provider
final categoryLoadingStateProvider = Provider<bool>((ref) {
  final asyncCategories = ref.watch(categoryNotifierProvider);
  return asyncCategories.maybeWhen(
    loading: () => true,
    orElse: () => false,
  );
});

/// 카테고리 에러 상태를 제공하는 Provider
final categoryErrorStateProvider = Provider<String?>((ref) {
  final asyncCategories = ref.watch(categoryNotifierProvider);
  return asyncCategories.maybeWhen(
    error: (error, _) => error.toString(),
    orElse: () => null,
  );
});

/// 특정 ID의 카테고리를 가져오는 Provider
final categoryByIdProvider = Provider.family<Category?, int>((ref, id) {
  final categories = ref.watch(currentCategoriesProvider);
  try {
    return categories.firstWhere((category) => category.id == id);
  } catch (e) {
    return null;
  }
});

/// 카테고리 개수를 제공하는 Provider
final categoryCountProvider = Provider<int>((ref) {
  final categories = ref.watch(currentCategoriesProvider);
  return categories.length;
});
