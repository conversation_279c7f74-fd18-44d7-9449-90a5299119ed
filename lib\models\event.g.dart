// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Event _$EventFromJson(Map<String, dynamic> json) => _Event(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String,
  imagePath: json['imagePath'] as String?,
  startDate: DateTime.parse(json['startDate'] as String),
  endDate: DateTime.parse(json['endDate'] as String),
  isActive: json['isActive'] as bool? ?? true,
  description: json['description'] as String?,
  revenueGoalMode:
      $enumDecodeNullable(_$RevenueGoalModeEnumMap, json['revenueGoalMode']) ??
      RevenueGoalMode.overall,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  ownerUserId: json['ownerUserId'] as String?,
  isOwner: json['isOwner'] as bool? ?? true,
  realtimeSyncEnabled: json['realtimeSyncEnabled'] as bool? ?? false,
  syncEnabledByUserId: json['syncEnabledByUserId'] as String?,
  syncSettingUpdatedAt: json['syncSettingUpdatedAt'] == null
      ? null
      : DateTime.parse(json['syncSettingUpdatedAt'] as String),
);

Map<String, dynamic> _$EventToJson(_Event instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'imagePath': instance.imagePath,
  'startDate': instance.startDate.toIso8601String(),
  'endDate': instance.endDate.toIso8601String(),
  'isActive': instance.isActive,
  'description': instance.description,
  'revenueGoalMode': _$RevenueGoalModeEnumMap[instance.revenueGoalMode]!,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'ownerUserId': instance.ownerUserId,
  'isOwner': instance.isOwner,
  'realtimeSyncEnabled': instance.realtimeSyncEnabled,
  'syncEnabledByUserId': instance.syncEnabledByUserId,
  'syncSettingUpdatedAt': instance.syncSettingUpdatedAt?.toIso8601String(),
};

const _$RevenueGoalModeEnumMap = {
  RevenueGoalMode.overall: 'overall',
  RevenueGoalMode.seller: 'seller',
};
