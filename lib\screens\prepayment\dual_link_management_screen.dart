import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/prepayment_virtual_product_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/prepayment_product_link_provider.dart';
import '../../models/prepayment_virtual_product.dart';
import '../../models/product.dart';
import '../../models/prepayment_product_link.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../providers/category_provider.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/app_bar_styles.dart';

/// 듀얼 리스트 기반 선입금 상품 ↔ 실제 상품 연동 관리 화면 (1차 경량 버전)
class DualLinkManagementScreen extends ConsumerStatefulWidget {
  const DualLinkManagementScreen({super.key});

  @override
  ConsumerState<DualLinkManagementScreen> createState() => _DualLinkManagementScreenState();
}

class _DualLinkManagementScreenState extends ConsumerState<DualLinkManagementScreen> {
  // 선택 상태
  final Set<int> _selectedVirtualIds = <int>{};
  final Set<int> _selectedProductIds = <int>{};

  // 검색
  String _virtualSearch = '';
  String _productSearch = '';

  bool _showUnlinkedVirtualOnly = false;
  bool _showUnlinkedProductsOnly = false;
  bool _initialLoading = true;
  String? _initialError;

  @override
  void initState() {
    super.initState();
    // 최초 로딩: 기존 탭에서 하던 load 호출을 여기에서 수행
    Future.microtask(() async {
      try {
        await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
        await ref.read(productNotifierProvider.notifier).loadProducts();
        await ref.read(prepaymentProductLinkNotifierProvider.notifier).loadLinks();
      } catch (e) {
        _initialError = e.toString();
      } finally {
        if (mounted) setState(() => _initialLoading = false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final vpState = ref.watch(prepaymentVirtualProductNotifierProvider);
    final productsState = ref.watch(productNotifierProvider);
    final links = ref.watch(prepaymentProductLinkNotifierProvider);

    if (_initialLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }
    if (_initialError != null) {
      return Scaffold(
        appBar: AppBar(title: Builder(builder: (ctx)=> Text('연동 관리', style: AppBarStyles.of(ctx)))),
        body: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 40),
              const SizedBox(height: 12),
              Text('초기 로딩 실패\n$_initialError', textAlign: TextAlign.center),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() { _initialLoading = true; _initialError = null; });
                  Future.microtask(() async {
                    try {
                      await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
                      await ref.read(productNotifierProvider.notifier).loadProducts();
                      await ref.read(prepaymentProductLinkNotifierProvider.notifier).loadLinks();
                    } catch (e) {
                      _initialError = e.toString();
                    } finally {
                      if (mounted) setState(() => _initialLoading = false);
                    }
                  });
                },
                child: const Text('재시도'),
              )
            ],
          ),
        ),
      );
    }

    final virtualProducts = _filteredVirtualProducts(vpState.virtualProducts, links);
    final products = _filteredProducts(productsState.products, links);

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _buildHeaderArea(
              virtualCount: vpState.virtualProducts.length,
              productCount: productsState.products.length,
              linkCount: links.length,
              links: links,
            ),
            const Divider(height: 1),
            Expanded(
              child: Row(
                children: [
                  Expanded(child: _buildVirtualPanel(virtualProducts, links)),
                  const VerticalDivider(width: 1),
                  Expanded(child: _buildProductPanel(products, links)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<PrepaymentVirtualProduct> _filteredVirtualProducts(List<PrepaymentVirtualProduct> source, List<PrepaymentProductLink> links) {
    var list = source;
    if (_virtualSearch.isNotEmpty) {
      list = list.where((v) => v.name.toLowerCase().contains(_virtualSearch.toLowerCase())).toList();
    }
    if (_showUnlinkedVirtualOnly) {
      final linkedSet = links.map((l) => l.virtualProductId).toSet();
      list = list.where((v) => !linkedSet.contains(v.id)).toList();
    }
    return list;
  }

  List<Product> _filteredProducts(List<Product> source, List<PrepaymentProductLink> links) {
    var list = source;
    if (_productSearch.isNotEmpty) {
      list = list.where((p) => p.name.toLowerCase().contains(_productSearch.toLowerCase())).toList();
    }
    if (_showUnlinkedProductsOnly) {
      final linkedSet = links.map((l) => l.productId).toSet();
      list = list.where((p) => !linkedSet.contains(p.id)).toList();
    }
    return list;
  }

  Widget _buildVirtualPanel(List<PrepaymentVirtualProduct> vps, List<PrepaymentProductLink> links) {
    return Column(
      children: [
        _buildSearchBar(
          hint: '선입금 상품 검색',
          value: _virtualSearch,
          onChanged: (v) => setState(() => _virtualSearch = v),
          trailing: FilterChip(
            label: const Text('미연동만'),
            selected: _showUnlinkedVirtualOnly,
            onSelected: (s) => setState(() => _showUnlinkedVirtualOnly = s),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: vps.length,
            itemBuilder: (context, index) {
              final vp = vps[index];
              final selected = _selectedVirtualIds.contains(vp.id);
              final linkedCount = links.where((l) => l.virtualProductId == vp.id).length;
              return ListTile(
                selected: selected,
                title: Text(vp.name),
                subtitle: linkedCount > 0 ? Text('연동 ${linkedCount}개') : const Text('미연동'),
                leading: Checkbox(
                  value: selected,
                  onChanged: (v) {
                    setState(() {
                      if (v == true) {
                        _selectedVirtualIds
                          ..clear()
                          ..add(vp.id); // 단일 선택 유지
                      } else {
                        _selectedVirtualIds.clear();
                      }
                    });
                  },
                ),
                onTap: () {
                  setState(() {
                    if (selected) {
                      _selectedVirtualIds.clear();
                    } else {
                      _selectedVirtualIds
                        ..clear()
                        ..add(vp.id);
                    }
                  });
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProductPanel(List<Product> products, List<PrepaymentProductLink> links) {
    return Column(
      children: [
        _buildSearchBar(
          hint: '실제 상품 검색',
          value: _productSearch,
          onChanged: (v) => setState(() => _productSearch = v),
          trailing: FilterChip(
            label: const Text('미연동만'),
            selected: _showUnlinkedProductsOnly,
            onSelected: (s) => setState(() => _showUnlinkedProductsOnly = s),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: products.length,
            itemBuilder: (context, index) {
              final p = products[index];
              final selected = _selectedProductIds.contains(p.id);
              final linkedCount = links.where((l) => l.productId == p.id).length;
              final category = ref.read(categoryByIdProvider(p.categoryId));
              return ListTile(
                selected: selected,
                title: Text(category != null ? '${category.name} - ${p.name}' : p.name),
                subtitle: linkedCount > 0 ? Text('연동 ${linkedCount}개') : const Text('미연동'),
                leading: Checkbox(
                  value: selected,
                  onChanged: (v) {
                    setState(() {
                      if (v == true) {
                        _selectedProductIds
                          ..clear()
                          ..add(p.id!);
                      } else {
                        _selectedProductIds.clear();
                      }
                    });
                  },
                ),
                onTap: () {
                  setState(() {
                    if (selected) {
                      _selectedProductIds.clear();
                    } else {
                      _selectedProductIds
                        ..clear()
                        ..add(p.id!);
                    }
                  });
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchBar({required String hint, required String value, required ValueChanged<String> onChanged, required Widget trailing}) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 4),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: hint,
                isDense: true,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              onChanged: onChanged,
            ),
          ),
          const SizedBox(width: 8),
          trailing,
        ],
      ),
    );
  }

  bool _canLink(List<PrepaymentProductLink> links) {
    return _selectedVirtualIds.isNotEmpty && _selectedProductIds.isNotEmpty;
  }

  bool _canUnlink(List<PrepaymentProductLink> links) {
    if (_selectedVirtualIds.isEmpty && _selectedProductIds.isEmpty) return false;
    // 둘 중 하나만 선택된 경우에도 해당 그룹과 연결된 링크 해제 허용
    return true;
  }

  Future<void> _performLink(List<PrepaymentProductLink> links) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return;
    final eventId = currentWorkspace.id;
    final toCreate = <PrepaymentProductLink>[];
    for (final vid in _selectedVirtualIds) {
      for (final pid in _selectedProductIds) {
        final exists = links.any((l) => l.virtualProductId == vid && l.productId == pid);
        if (!exists) {
          toCreate.add(PrepaymentProductLink(
            virtualProductId: vid,
            productId: pid,
            linkedAt: DateTime.now(),
            quantity: 1,
            eventId: eventId,
          ));
        }
      }
    }
    if (toCreate.isEmpty) return;
    LoggerUtils.logInfo('배치 연동 생성: ${toCreate.length}개', tag: 'DualLink');
    await ref.read(prepaymentProductLinkNotifierProvider.notifier).batchAddLinks(toCreate);
    if (mounted) {
      ToastUtils.showSuccess(context, '연동 ${toCreate.length}개 생성');
    }
  }

  Future<void> _performUnlink(List<PrepaymentProductLink> links) async {
    final toRemove = links.where((l) =>
      (_selectedVirtualIds.isEmpty || _selectedVirtualIds.contains(l.virtualProductId)) &&
      (_selectedProductIds.isEmpty || _selectedProductIds.contains(l.productId))
    ).toList();
    if (toRemove.isEmpty) return;
    LoggerUtils.logInfo('배치 연동 해제: ${toRemove.length}개', tag: 'DualLink');
    await ref.read(prepaymentProductLinkNotifierProvider.notifier).batchRemoveLinks(toRemove);
    if (mounted) {
      ToastUtils.showInfo(context, '연동 ${toRemove.length}개 해제');
    }
  }

  Widget _buildSummaryBar(int virtualCount, int productCount, int linkCount) {
    Text _item(String label, int value, Color color, IconData icon) {
      return Text.rich(
        TextSpan(children: [
          WidgetSpan(child: Icon(icon, size: 16, color: color)),
          const WidgetSpan(child: SizedBox(width: 4)),
          TextSpan(text: '$label: ', style: const TextStyle(fontWeight: FontWeight.w500)),
          TextSpan(text: '$value개', style: TextStyle(color: color, fontWeight: FontWeight.bold)),
        ]),
      );
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _item('선입금', virtualCount, Colors.blue, Icons.inventory_2_outlined),
          const SizedBox(width: 20),
          _item('상품', productCount, Colors.green, Icons.shopping_bag_outlined),
          const SizedBox(width: 20),
          _item('링크', linkCount, Colors.orange, Icons.link),
        ],
      ),
    );
  }

  Widget _buildHeaderArea({
    required int virtualCount,
    required int productCount,
    required int linkCount,
    required List<PrepaymentProductLink> links,
  }) {
  final canLink = _canLink(links);
  final canUnlink = _canUnlink(links);
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.4),
        border: Border(
          bottom: BorderSide(color: Theme.of(context).dividerColor.withOpacity(0.4)),
        ),
      ),
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 6),
      child: Column(
        children: [
      // Summary bar only (링크 목록 버튼은 상위 AppBar 로 이동)
      _buildSummaryBar(virtualCount, productCount, linkCount),
          const SizedBox(height: 6),
          // Action buttons row (always visible)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: canLink ? () => _performLink(links) : null,
                icon: const Icon(Icons.link),
                label: const Text('연동'),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: canUnlink ? () => _performUnlink(links) : null,
                icon: const Icon(Icons.link_off),
                label: const Text('해제'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              OutlinedButton.icon(
                onPressed: (_selectedVirtualIds.isNotEmpty || _selectedProductIds.isNotEmpty)
                    ? () => setState(() { _selectedVirtualIds.clear(); _selectedProductIds.clear(); })
                    : null,
                icon: const Icon(Icons.clear),
                label: const Text('선택 해제'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 링크 목록 다이얼로그 기능은 상위 AppBar 액션에서 showPrepaymentLinkListDialog 로 제공
}
