import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/admin_models.dart';
import 'cache_service.dart';
import '../utils/logger_utils.dart';

/// 관리자 API 서비스
class AdminService {
  static const String _baseUrl = 'https://us-central1-parabara-1a504.cloudfunctions.net';
  static const String _tokenKey = 'admin_token';
  
  // API URLs
  static const Map<String, String> _apiUrls = {
    'adminAuth': 'https://adminauth-kahfshl2oa-uc.a.run.app',
    'adminDashboard': 'https://admindashboard-kahfshl2oa-uc.a.run.app',
    'adminUsers': 'https://adminusers-kahfshl2oa-uc.a.run.app',
    'adminToggleSubscription': '$_baseUrl/adminToggleSubscription',
    'getUserUsageStats': '$_baseUrl/getUserUsageStats',
    'getAdminSystemInfo': '$_baseUrl/getAdminSystemInfo',
  };

  /// 관리자 로그인
  static Future<AdminAuthResponse> login(String username, String password) async {
    try {
      final response = await http.post(
        Uri.parse(_apiUrls['adminAuth']!),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': username,
          'password': password,
        }),
      );

      final data = jsonDecode(response.body);
      final authResponse = AdminAuthResponse.fromJson(data);

      // 토큰 저장
      if (authResponse.success && authResponse.token != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_tokenKey, authResponse.token!);
      }

      return authResponse;
    } catch (e) {
      return AdminAuthResponse(
        success: false,
        message: '로그인 중 오류가 발생했습니다: $e',
      );
    }
  }

  /// 저장된 토큰 가져오기
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  /// 로그아웃
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
  }

  /// 인증 헤더 생성
  static Future<Map<String, String>> _getAuthHeaders() async {
    final token = await getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// 대시보드 통계 조회 (캐싱 적용)
  static Future<AdminDashboardStats?> getDashboardStats({bool forceRefresh = false}) async {
    const cacheKey = CacheService.adminDashboardStatsKey;
    const cacheDuration = Duration(hours: 24); // 24시간 캐시 (수동 새로고침 방식)

    try {
      // 강제 새로고침이 아닌 경우에만 캐시 확인
      if (!forceRefresh) {
        final cachedStats = await CacheService.get<AdminDashboardStats>(
          cacheKey,
          (json) => AdminDashboardStats.fromJson(json),
        );

        if (cachedStats != null) {
          LoggerUtils.logDebug('대시보드 통계 캐시 사용', tag: 'AdminService');
          return cachedStats;
        }
      }

      // 캐시 미스 - API 호출
      LoggerUtils.logDebug('대시보드 통계 API 호출', tag: 'AdminService');
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse(_apiUrls['adminDashboard']!),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          final stats = AdminDashboardStats.fromJson(data['data']);

          // 캐시에 저장
          await CacheService.set<AdminDashboardStats>(
            cacheKey,
            stats,
            cacheDuration,
            (stats) => stats.toJson(),
          );

          return stats;
        }
      }
      return null;
    } catch (e) {
      LoggerUtils.logError('대시보드 통계 조회 오류', tag: 'AdminService', error: e);
      return null;
    }
  }

  /// 사용자 목록 조회 (페이지네이션 포함, 캐싱 적용)
  static Future<AdminUsersResponse> getUsers({int page = 1, int limit = 20}) async {
    final cacheKey = CacheService.generateKey(CacheService.adminUsersKey, {
      'page': page,
      'limit': limit,
    });
    const cacheDuration = Duration(hours: 1); // 1시간 캐시 (사용자 목록은 상대적으로 자주 변경)

    try {
      // 캐시에서 먼저 확인
      final cachedResponse = await CacheService.get<AdminUsersResponse>(
        cacheKey,
        (json) => AdminUsersResponse.fromJson(json),
      );

      if (cachedResponse != null) {
        LoggerUtils.logDebug('사용자 목록 캐시 사용 (page: $page)', tag: 'AdminService');
        return cachedResponse;
      }

      // 캐시 미스 - API 호출
      LoggerUtils.logDebug('사용자 목록 API 호출 (page: $page)', tag: 'AdminService');
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('${_apiUrls['adminUsers']!}?page=$page&limit=$limit'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          final users = (data['data']['users'] as List)
              .map((user) => AdminUser.fromJson(user))
              .toList();

          // 각 사용자의 사용량 데이터 로드
          await _loadUsageDataForUsers(users);

          final pagination = data['data']['pagination'];
          final usersResponse = AdminUsersResponse(
            users: users,
            pagination: AdminPagination.fromJson(pagination),
          );

          // 캐시에 저장
          await CacheService.set<AdminUsersResponse>(
            cacheKey,
            usersResponse,
            cacheDuration,
            (response) => response.toJson(),
          );

          return usersResponse;
        }
      }
      return AdminUsersResponse(users: [], pagination: AdminPagination.empty());
    } catch (e) {
      LoggerUtils.logError('사용자 목록 조회 오류', tag: 'AdminService', error: e);
      return AdminUsersResponse(users: [], pagination: AdminPagination.empty());
    }
  }

  /// 사용자 목록 조회 (하위 호환성을 위해 유지)
  static Future<List<AdminUser>> getUsersLegacy() async {
    final response = await getUsers();
    return response.users;
  }

  /// 시스템 모니터링 정보 조회
  static Future<Map<String, dynamic>?> getSystemInfo() async {
    try {
      final response = await http.get(
        Uri.parse(_apiUrls['getAdminSystemInfo']!),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return data['data'];
        }
      }
      return null;
    } catch (e) {
      print('시스템 정보 조회 오류: $e');
      return null;
    }
  }

  /// 사용자들의 사용량 데이터 로드
  static Future<void> _loadUsageDataForUsers(List<AdminUser> users) async {
    final futures = users.map((user) async {
      try {
        final response = await http.get(
          Uri.parse('${_apiUrls['getUserUsageStats']}?uid=${user.uid}&days=7'),
        );

        if (response.statusCode == 200) {
          final usageData = jsonDecode(response.body);
          if (usageData['success'] == true) {
            // 사용량 정보 업데이트 (불변 객체이므로 새로 생성해야 하지만 여기서는 간단히 처리)
            // 실제로는 copyWith 메서드를 구현하거나 다른 방식으로 처리해야 함
          }
        }
      } catch (e) {
        print('사용자 ${user.uid} 사용량 로드 오류: $e');
      }
    });

    await Future.wait(futures);
  }

  /// 구독 상태 변경
  static Future<bool> toggleSubscription(String uid, String currentStatus) async {
    try {
      final headers = await _getAuthHeaders();
      final action = currentStatus == 'active' ? 'deactivate' : 'activate';
      
      final response = await http.post(
        Uri.parse(_apiUrls['adminToggleSubscription']!),
        headers: headers,
        body: jsonEncode({
          'uid': uid,
          'action': action,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] == true;
      }
      return false;
    } catch (e) {
      print('구독 상태 변경 오류: $e');
      return false;
    }
  }

  /// Firestore timestamp 파싱 헬퍼 메서드
  static DateTime _parseTimestamp(dynamic timestamp) {
    try {
      if (timestamp == null) return DateTime.now();

      // Firestore Timestamp 객체인 경우
      if (timestamp is Map && timestamp.containsKey('_seconds')) {
        final seconds = timestamp['_seconds'] as int;
        final nanoseconds = timestamp['_nanoseconds'] as int? ?? 0;
        return DateTime.fromMillisecondsSinceEpoch(
          seconds * 1000 + (nanoseconds / 1000000).round()
        );
      }

      // ISO 문자열인 경우
      if (timestamp is String) {
        return DateTime.parse(timestamp);
      }

      // Unix timestamp (밀리초)인 경우
      if (timestamp is int) {
        return DateTime.fromMillisecondsSinceEpoch(
          timestamp > 1000000000000 ? timestamp : timestamp * 1000
        );
      }

      // 기본값
      return DateTime.now();
    } catch (e) {
      print('Timestamp 파싱 오류: $e, timestamp: $timestamp');
      return DateTime.now();
    }
  }

  /// 관리자 로그 조회 (실데이터)
  static Future<List<SystemLog>> getAdminLogs() async {
    try {
      final systemInfo = await getSystemInfo();
      if (systemInfo != null && systemInfo['adminLogs'] != null) {
        final logsData = systemInfo['adminLogs'] as List;
        return logsData.map((log) => SystemLog(
          timestamp: _parseTimestamp(log['timestamp']),
          level: log['success'] == true ? 'SUCCESS' : 'INFO',
          message: '관리자 로그인',
          details: '${log['username']} - ${log['ip']}',
        )).toList();
      }
    } catch (e) {
      print('관리자 로그 조회 실패: $e');
    }

    return [];
  }

  /// 자동 결제 로그 조회 (실데이터)
  static Future<List<SystemLog>> getAutoPaymentLogs() async {
    try {
      final systemInfo = await getSystemInfo();
      if (systemInfo != null && systemInfo['autoPaymentLogs'] != null) {
        final logsData = systemInfo['autoPaymentLogs'] as List;
        return logsData.map((log) => SystemLog(
          timestamp: _parseTimestamp(log['timestamp']),
          level: log['level'] ?? 'INFO',
          message: log['message'] ?? '자동 결제 처리',
          details: '처리: ${log['totalProcessed']}건, 성공: ${log['successCount']}건, 실패: ${log['failureCount']}건',
        )).toList();
      }
    } catch (e) {
      print('자동 결제 로그 조회 실패: $e');
    }

    return [];
  }

  /// 접속 로그 조회 (실데이터)
  static Future<List<SystemLog>> getAccessLogs() async {
    try {
      final systemInfo = await getSystemInfo();
      if (systemInfo != null && systemInfo['accessLogs'] != null) {
        final logsData = systemInfo['accessLogs'] as List;
        return logsData.map((log) => SystemLog(
          timestamp: _parseTimestamp(log['timestamp']),
          level: 'INFO',
          message: '사용자 접속',
          details: '${log['userId'] ?? 'Unknown'} - ${log['ip'] ?? 'Unknown IP'}',
        )).toList();
      }
    } catch (e) {
      print('접속 로그 조회 실패: $e');
    }

    return [];
  }

  /// 시스템 로그 조회 (하위 호환성을 위해 유지)
  static Future<List<SystemLog>> getSystemLogs() async {
    return await getAdminLogs();
  }

  /// 시스템 통계 조회 (실데이터)
  static Future<SystemStats> getSystemStats() async {
    try {
      final systemInfo = await getSystemInfo();
      if (systemInfo != null && systemInfo['systemStats'] != null) {
        final stats = systemInfo['systemStats'];
        return SystemStats(
          totalFunctionCalls: stats['totalFunctionCalls'] ?? '-',
          successRate: stats['successRate'] ?? '-',
          lastExecution: stats['lastExecution'] ?? '-',
        );
      }
    } catch (e) {
      print('실데이터 통계 조회 실패, 기본 데이터 반환: $e');
    }

    // 실데이터 조회 실패 시 기본 데이터 반환
    return SystemStats(
      totalFunctionCalls: '-',
      successRate: '-',
      lastExecution: '-',
    );
  }

  /// 실제 클라이언트 IP 조회
  static Future<String?> getClientIp() async {
    try {
      // 여러 IP 조회 서비스를 시도 (DNS 해결 실패에 대비해 더 짧은 타임아웃 사용)
      final services = [
        'https://api.ipify.org',
        'https://ipinfo.io/ip',
        'https://icanhazip.com',
      ];

      for (final service in services) {
        try {
          print('IP 조회 시도: $service');
          final response = await http.get(
            Uri.parse(service),
            headers: {
              'Accept': 'text/plain',
              'User-Agent': 'Flutter App',
            },
          ).timeout(const Duration(seconds: 3)); // 타임아웃을 3초로 단축

          if (response.statusCode == 200) {
            final ip = response.body.trim();
            if (ip.isNotEmpty && ip != 'Unknown' && _isValidIp(ip)) {
              print('IP 조회 성공: $ip (서비스: $service)');
              return ip;
            }
          } else {
            print('IP 조회 실패: ${response.statusCode} (서비스: $service)');
          }
        } on SocketException catch (e) {
          print('DNS 해결 실패: $service - $e');
          // DNS 해결 실패 시 다음 서비스로 즉시 이동
          continue;
        } on TimeoutException catch (e) {
          print('IP 조회 타임아웃: $service - $e');
          continue;
        } catch (e) {
          print('IP 조회 서비스 오류: $service - $e');
          // 다음 서비스 시도
          continue;
        }
      }

      print('모든 IP 조회 서비스 실패 - Unknown으로 처리');
      return 'Unknown'; // null 대신 'Unknown' 반환으로 로그 기록이 계속 진행되도록
    } catch (e) {
      print('IP 조회 전체 오류: $e - Unknown으로 처리');
      return 'Unknown'; // null 대신 'Unknown' 반환
    }
  }

  /// IP 주소 유효성 검사
  static bool _isValidIp(String ip) {
    // IPv4 정규식 검사
    final ipv4Regex = RegExp(r'^(\d{1,3}\.){3}\d{1,3}$');
    if (ipv4Regex.hasMatch(ip)) {
      final parts = ip.split('.');
      return parts.every((part) {
        final num = int.tryParse(part);
        return num != null && num >= 0 && num <= 255;
      });
    }

    // IPv6는 간단한 검사만 (콜론 포함 여부)
    return ip.contains(':') && ip.length > 7;
  }

  /// 접속 로그 기록
  static Future<void> logAccess(String userId, String? ip) async {
    try {
      // IP가 제공되지 않았다면 실제 IP 조회 시도 (더 짧은 타임아웃으로 빠른 실패)
      String? actualIp = ip;
      if (actualIp == null || actualIp == 'Unknown') {
        try {
          actualIp = await getClientIp().timeout(
            const Duration(seconds: 5), // 타임아웃을 5초로 단축
            onTimeout: () {
              print('IP 조회 타임아웃 - Unknown으로 처리');
              return 'Unknown';
            },
          );
        } on SocketException catch (e) {
          print('DNS 해결 실패로 IP 조회 불가: $e - Unknown으로 처리');
          actualIp = 'Unknown';
        } catch (e) {
          print('IP 조회 중 오류 발생: $e - Unknown으로 처리');
          actualIp = 'Unknown';
        }
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/logAccess'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'userId': userId,
          'ip': actualIp ?? 'Unknown',
          'timestamp': DateTime.now().toIso8601String(),
          'userAgent': 'Flutter App',
        }),
      ).timeout(const Duration(seconds: 10)); // 로그 기록 타임아웃도 단축

      if (response.statusCode != 200) {
        print('접속 로그 기록 실패: ${response.statusCode}');
      } else {
        print('접속 로그 기록 성공: $userId (IP: ${actualIp ?? 'Unknown'})');
      }
    } catch (e) {
      print('접속 로그 기록 오류: $e');
      // 로그 기록 실패는 앱 동작에 영향을 주지 않도록 조용히 처리
      // Google 로그인 등 핵심 기능에는 영향을 주지 않음
    }
  }
}
