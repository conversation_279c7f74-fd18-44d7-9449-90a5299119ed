{"indexes": [{"collectionGroup": "prepayment_virtual_products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "grantedAt", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "prepayments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "set_discounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "event_invitations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "eventId", "order": "ASCENDING"}, {"fieldPath": "expiresAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "subscriptions", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "nextPaymentDate", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "subscriptions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "nextPaymentDate", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "revenue_goals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "checklist_items", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "sales_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "prepayment_product_links", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "event_invitations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "eventId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "event_invitations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "invitationCode", "order": "ASCENDING"}, {"fieldPath": "expiresAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "event_invitations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "respondedAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}], "fieldOverrides": []}