import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/event.dart';
import '../models/event_sort_option.dart';
import '../models/category.dart' as model_category;
import '../models/seller.dart';
import '../services/database_service.dart';
import '../repositories/seller_repository.dart';
import '../providers/seller_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../utils/network_status.dart';
import '../utils/logger_utils.dart';
import '../utils/event_id_generator.dart';

/// Event 데이터 접근을 담당하는 Repository 클래스입니다.
/// 
/// 주요 기능:
/// - Event CRUD 작업
/// - 검색 및 필터링
/// - 정렬 기능
/// - 기본 행사 관리
class EventRepository {
  static const String _tag = 'EventRepository';
  final DatabaseService _databaseService;
  final SellerRepository? _sellerRepository;

  EventRepository(this._databaseService, [this._sellerRepository]);

  /// 모든 행사를 조회합니다.
  Future<List<Event>> getAllEvents({
    EventFilter filter = EventFilter.defaultFilter,
  }) async {
    try {
      LoggerUtils.methodStart('getAllEvents', tag: _tag);
      
      final db = await _databaseService.database;
      
      // WHERE 조건 구성
      final whereConditions = <String>[];
      final whereArgs = <dynamic>[];
      
      // 검색 키워드 필터
      if (filter.searchKeyword.isNotEmpty) {
        whereConditions.add('(name LIKE ? OR description LIKE ? OR location LIKE ?)');
        final keyword = '%${filter.searchKeyword}%';
        whereArgs.addAll([keyword, keyword, keyword]);
      }
      
      // 활성 상태 필터
      if (filter.onlyActive) {
        whereConditions.add('isActive = ?');
        whereArgs.add(1);
      }
      
      // 날짜 범위 필터
      if (filter.dateRangeStart != null) {
        whereConditions.add('endDate >= ?');
        whereArgs.add(filter.dateRangeStart!.toIso8601String());
      }
      
      if (filter.dateRangeEnd != null) {
        whereConditions.add('startDate <= ?');
        whereArgs.add(filter.dateRangeEnd!.toIso8601String());
      }
      
      // 상태별 필터 (진행중/예정/종료)
      final now = DateTime.now();
      if (filter.onlyOngoing) {
        whereConditions.add('startDate <= ? AND endDate >= ?');
        whereArgs.addAll([now.toIso8601String(), now.toIso8601String()]);
      } else if (filter.onlyUpcoming) {
        whereConditions.add('startDate > ?');
        whereArgs.add(now.toIso8601String());
      } else if (filter.onlyEnded) {
        whereConditions.add('endDate < ?');
        whereArgs.add(now.toIso8601String());
      }
      
      // 쿼리 실행
      final result = await db.query(
        DatabaseServiceImpl.eventsTable,
        where: whereConditions.isNotEmpty ? whereConditions.join(' AND ') : null,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: filter.sortOption.sqlOrderBy,
      );
      
      final events = result.map((map) => Event.fromMap(map)).toList();
      
      LoggerUtils.logInfo('행사 ${events.length}개 조회 완료', tag: _tag);
      LoggerUtils.methodEnd('getAllEvents', tag: _tag);
      
      return events;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 목록 조회 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// ID로 특정 행사를 조회합니다.
  Future<Event?> getEventById(int id) async {
    try {
      LoggerUtils.methodStart('getEventById', tag: _tag, data: {'id': id});
      
      final db = await _databaseService.database;
      final result = await db.query(
        DatabaseServiceImpl.eventsTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );
      
      if (result.isNotEmpty) {
        final event = Event.fromMap(result.first);
        LoggerUtils.logInfo('행사 조회 성공: ${event.name}', tag: _tag);
        LoggerUtils.methodEnd('getEventById', tag: _tag);
        return event;
      }
      
      LoggerUtils.logWarning('행사를 찾을 수 없음: ID $id', tag: _tag);
      LoggerUtils.methodEnd('getEventById', tag: _tag);
      return null;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        'ID로 행사 조회 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': id},
      );
      rethrow;
    }
  }

  /// 새로운 행사를 추가합니다.
  Future<Event> insertEvent(Event event) async {
    try {
      LoggerUtils.methodStart('insertEvent', tag: _tag, data: {'name': event.name});

      final db = await _databaseService.database;

      // 글로벌 유니크 ID 생성 (AUTOINCREMENT 대신)
      final uniqueId = EventIdGenerator.generate();
      final eventWithId = event.copyWith(id: uniqueId);

      await db.insert(
        DatabaseServiceImpl.eventsTable,
        eventWithId.toMap(),
      );

      final insertedEvent = eventWithId;

      // 새 행사에 기본 카테고리 자동 생성
      await _createDefaultCategoryForEvent(db, uniqueId);

      // 새 행사에 기본 판매자 자동 생성
      await _createDefaultSellerForEvent(uniqueId);

      LoggerUtils.logInfo('행사 추가 성공: ${insertedEvent.name} (ID: $uniqueId)', tag: _tag);
      LoggerUtils.methodEnd('insertEvent', tag: _tag);

      // 실시간 동기화는 EventNotifier에서 처리하므로 여기서는 하지 않음 (중복 방지)
      // Firebase 업로드는 EventNotifier.addEvent()에서 dataSyncService.uploadSingleEvent()로 처리됨

      return insertedEvent;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 추가 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'name': event.name},
      );
      rethrow;
    }
  }

  /// 새로운 행사에 기본 카테고리를 생성합니다.
  Future<void> _createDefaultCategoryForEvent(dynamic db, int eventId) async {
    try {
      final defaultCategory = model_category.Category.createDefault(eventId: eventId);

      final id = await db.insert(
        DatabaseServiceImpl.categoriesTable,
        defaultCategory.toMap(),
      );

      // ID가 설정된 카테고리 객체 생성
      final insertedCategory = defaultCategory.copyWith(id: id);

      LoggerUtils.logInfo('행사 ID $eventId에 기본 카테고리 생성 완료 (ID: $id)', tag: _tag);

      // Firebase에 기본 카테고리 업로드 (백그라운드에서 비동기 실행)
      _syncDefaultCategoryInBackground(insertedCategory);

    } catch (e) {
      LoggerUtils.logError('기본 카테고리 생성 실패', error: e, tag: _tag);
      // 카테고리 생성 실패해도 행사 생성은 계속 진행
    }
  }

  /// 백그라운드에서 기본 카테고리 Firebase 동기화를 수행합니다
  void _syncDefaultCategoryInBackground(model_category.Category category) {
    if (!NetworkStatusUtil.isOnline) return;


  }

  /// 행사 정보를 업데이트합니다.
  Future<Event> updateEvent(Event event) async {
    try {
      LoggerUtils.methodStart('updateEvent', tag: _tag, data: {'id': event.id, 'name': event.name});
      
      if (event.id == null) {
        throw ArgumentError('업데이트할 행사의 ID가 필요합니다');
      }
      
      final db = await _databaseService.database;
      
      final updatedEvent = event.copyWith(updatedAt: DateTime.now());
      
      final rowsAffected = await db.update(
        DatabaseServiceImpl.eventsTable,
        updatedEvent.toMap(),
        where: 'id = ?',
        whereArgs: [event.id],
      );
      
      if (rowsAffected == 0) {
        throw Exception('업데이트할 행사를 찾을 수 없습니다: ID ${event.id}');
      }

      LoggerUtils.logInfo('행사 업데이트 성공: ${updatedEvent.name}', tag: _tag);
      LoggerUtils.methodEnd('updateEvent', tag: _tag);

      // 실시간 동기화는 EventNotifier에서 처리하므로 여기서는 하지 않음 (중복 방지)
      // Firebase 업로드는 EventNotifier.updateEvent()에서 dataSyncService.uploadSingleEvent()로 처리됨

      return updatedEvent;
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 업데이트 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': event.id, 'name': event.name},
      );
      rethrow;
    }
  }

  /// 행사를 삭제합니다. 
  /// 단, 행사가 1개만 있을 때는 삭제할 수 없습니다.
  Future<void> deleteEvent(int id) async {
    try {
      LoggerUtils.methodStart('deleteEvent', tag: _tag, data: {'id': id});
      
      final db = await _databaseService.database;
      
      // 전체 행사 개수 확인
      final countResult = await db.rawQuery('SELECT COUNT(*) as count FROM ${DatabaseServiceImpl.eventsTable}');
      final totalCount = countResult.first['count'] as int;
      
      if (totalCount <= 1) {
        throw Exception('마지막 행사는 삭제할 수 없습니다');
      }
      
      final rowsAffected = await db.delete(
        DatabaseServiceImpl.eventsTable,
        where: 'id = ?',
        whereArgs: [id],
      );
      
      if (rowsAffected == 0) {
        throw Exception('삭제할 행사를 찾을 수 없습니다: ID $id');
      }

      // 실시간 동기화는 EventNotifier에서 처리하므로 여기서는 하지 않음 (중복 방지)
      // Firebase 삭제는 EventNotifier.deleteEvent()에서 dataSyncService.deleteSingleEvent()로 처리됨

      LoggerUtils.logInfo('행사 삭제 성공: ID $id', tag: _tag);
      LoggerUtils.methodEnd('deleteEvent', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 삭제 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': id},
      );
      rethrow;
    }
  }

  /// 행사 개수를 조회합니다.
  Future<int> getEventCount({EventFilter? filter}) async {
    try {
      final events = await getAllEvents(filter: filter ?? EventFilter.defaultFilter);
      return events.length;
    } catch (e) {
      LoggerUtils.logError('행사 개수 조회 실패', tag: _tag, error: e);
      return 0;
    }
  }

  /// 행사명으로 검색합니다.
  Future<List<Event>> searchEventsByName(String name) async {
    final filter = EventFilter.defaultFilter.copyWith(searchKeyword: name);
    return getAllEvents(filter: filter);
  }

  /// 진행 중인 행사들을 조회합니다.
  Future<List<Event>> getOngoingEvents() async {
    final filter = EventFilter.defaultFilter.copyWith(onlyOngoing: true);
    return getAllEvents(filter: filter);
  }

  /// 예정된 행사들을 조회합니다.
  Future<List<Event>> getUpcomingEvents() async {
    final filter = EventFilter.defaultFilter.copyWith(onlyUpcoming: true);
    return getAllEvents(filter: filter);
  }

  /// 종료된 행사들을 조회합니다.
  Future<List<Event>> getEndedEvents() async {
    final filter = EventFilter.defaultFilter.copyWith(onlyEnded: true);
    return getAllEvents(filter: filter);
  }

  /// 모든 이벤트 삭제 (복원용)
  Future<void> deleteAllEvents() async {
    try {
      LoggerUtils.logInfo('모든 이벤트 삭제 시작', tag: _tag);

      final db = await _databaseService.database;
      final deletedCount = await db.delete(DatabaseServiceImpl.eventsTable);

      LoggerUtils.logInfo('모든 이벤트 삭제 완료: ${deletedCount}개', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '모든 이벤트 삭제 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 새 행사에 기본 판매자 자동 생성
  Future<void> _createDefaultSellerForEvent(int eventId) async {
    try {
      LoggerUtils.logInfo('새 행사에 기본 판매자 자동 생성 시작: eventId=$eventId', tag: _tag);

      // 현재 사용자의 닉네임 가져오기
      final nickname = await _getCurrentUserNickname();
      if (nickname == null || nickname.isEmpty) {
        LoggerUtils.logInfo('닉네임이 없어 기본 판매자 자동 생성을 건너뜁니다', tag: _tag);
        return;
      }

      // SellerRepository가 없으면 직접 생성
      final sellerRepository = _sellerRepository ?? SellerRepository(database: _databaseService);

      // 해당 행사에 이미 같은 이름의 기본 판매자가 있는지 확인
      final existingSellers = await sellerRepository.getSellersByEventId(eventId);
      final existingDefaultSeller = existingSellers.where((s) => s.name == nickname && s.isDefault).firstOrNull;

      if (existingDefaultSeller != null) {
        LoggerUtils.logInfo('이미 기본 판매자가 존재합니다: $nickname', tag: _tag);
        return;
      }

      // 기본 판매자 생성
      final defaultSeller = Seller.create(
        name: nickname,
        isDefault: true,
        eventId: eventId,
      );

      final sellerId = await sellerRepository.insertSeller(defaultSeller);
      LoggerUtils.logInfo('기본 판매자 자동 생성 완료: $nickname (ID: $sellerId)', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '기본 판매자 자동 생성 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'eventId': eventId},
      );
      // 기본 판매자 생성 실패해도 행사 생성은 계속 진행
    }
  }

  /// 현재 사용자의 닉네임 가져오기
  Future<String?> _getCurrentUserNickname() async {
    try {
      // 로컬 DB에서 닉네임 조회
      final db = await _databaseService.database;
      final result = await db.query('nicknames', limit: 1);

      if (result.isNotEmpty) {
        final nickname = result.first['name'] as String?;
        if (nickname != null && nickname.isNotEmpty) {
          return nickname;
        }
      }

      // 로컬 DB에 없으면 Firestore에서 조회
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final doc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
        if (doc.exists && doc.data() != null && doc.data()!['nickname'] != null) {
          return doc.data()!['nickname'] as String;
        }
      }

      return null;
    } catch (e) {
      LoggerUtils.logError('현재 사용자 닉네임 조회 실패', tag: _tag, error: e);
      return null;
    }
  }


}

/// EventRepository Provider
final eventRepositoryProvider = Provider<EventRepository>((ref) {
  final databaseService = ref.read(databaseServiceProvider);
  final sellerRepository = ref.read(sellerRepositoryProvider);
  return EventRepository(databaseService, sellerRepository);
});
