/// 바라 부스 매니저 - 초대 서비스
///
/// 행사 초대 관련 비즈니스 로직을 처리하는 서비스 클래스입니다.
/// - 초대 코드 생성 및 검증
/// - 초대 생성, 수락, 거절 처리
/// - 초대 만료 관리
/// - Firebase Firestore 연동
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'dart:async';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/event_invitation.dart';
import '../models/invitation_status.dart';
import '../utils/logger_utils.dart';

/// 초대 서비스 클래스
class InvitationService {
  static const String _tag = 'InvitationService';
  
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  /// 초대 코드 문자 집합 (혼동하기 쉬운 문자 제외)
  static const String _codeCharacters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
  
  /// 초대 코드 길이
  static const int _codeLength = 6;
  
  /// 초대 만료 시간 (2시간)
  static const Duration _invitationExpiry = Duration(hours: 2);

  /// 유니크한 초대 코드 생성
  /// 
  /// 6자리 영숫자 조합으로 생성하며, 혼동하기 쉬운 문자(0, O, I, 1)는 제외
  /// 중복 검사를 통해 유니크함을 보장
  Future<String> generateUniqueInvitationCode() async {
    LoggerUtils.methodStart('generateUniqueInvitationCode', tag: _tag);
    
    const maxAttempts = 10;
    int attempts = 0;
    
    while (attempts < maxAttempts) {
      final code = _generateRandomCode();
      
      // 중복 검사
      final isDuplicate = await _isCodeDuplicate(code);
      if (!isDuplicate) {
        LoggerUtils.logInfo('유니크한 초대 코드 생성 완료: $code', tag: _tag);
        return code;
      }
      
      attempts++;
      LoggerUtils.logWarning('초대 코드 중복, 재시도: $attempts/$maxAttempts', tag: _tag);
    }
    
    throw Exception('초대 코드 생성 실패: 최대 시도 횟수 초과');
  }

  /// 랜덤 코드 생성
  String _generateRandomCode() {
    final random = Random();
    final buffer = StringBuffer();
    
    for (int i = 0; i < _codeLength; i++) {
      final index = random.nextInt(_codeCharacters.length);
      buffer.write(_codeCharacters[index]);
    }
    
    return buffer.toString();
  }

  /// 코드 중복 검사
  Future<bool> _isCodeDuplicate(String code) async {
    try {
      final query = await _firestore
          .collection('event_invitations')
          .where('invitationCode', isEqualTo: code)
          .where('status', isEqualTo: InvitationStatus.pending.value)
          .limit(1)
          .get();
      
      return query.docs.isNotEmpty;
    } catch (e) {
      LoggerUtils.logError('코드 중복 검사 실패', tag: _tag, error: e);
      return false; // 오류 시 중복이 아닌 것으로 간주
    }
  }

  /// 초대 생성
  /// 
  /// [eventId] 행사 ID
  /// [eventName] 행사명
  /// [ownerNickname] 소유자 닉네임
  /// 반환값: 생성된 초대 정보
  Future<EventInvitation> createInvitation({
    required int eventId,
    required String eventName,
    required String ownerNickname,
  }) async {
    LoggerUtils.methodStart('createInvitation', tag: _tag, data: {
      'eventId': eventId,
      'eventName': eventName,
      'ownerNickname': ownerNickname,
    });

    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('로그인이 필요합니다');
    }

    try {
      // 기존 활성 초대가 있는지 확인
      await _expireExistingInvitations(eventId, user.uid);
      
      // 새 초대 코드 생성
      final invitationCode = await generateUniqueInvitationCode();
      
      // 초대 객체 생성
      final invitation = EventInvitation.create(
        invitationCode: invitationCode,
        eventId: eventId,
        eventName: eventName,
        ownerUserId: user.uid,
        ownerNickname: ownerNickname,
      );

      // Firestore에 저장
      await _firestore
          .collection('event_invitations')
          .doc(invitation.id)
          .set(invitation.toFirebaseMap());

      LoggerUtils.logInfo('초대 생성 완료: ${invitation.invitationCode}', tag: _tag);
      return invitation;
    } catch (e) {
      LoggerUtils.logError('초대 생성 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 기존 활성 초대들을 만료 처리
  Future<void> _expireExistingInvitations(int eventId, String ownerUserId) async {
    try {
      final query = await _firestore
          .collection('event_invitations')
          .where('eventId', isEqualTo: eventId)
          .where('ownerUserId', isEqualTo: ownerUserId)
          .where('status', isEqualTo: InvitationStatus.pending.value)
          .get();

      final batch = _firestore.batch();
      for (final doc in query.docs) {
        batch.update(doc.reference, {
          'status': InvitationStatus.expired.value,
          'respondedAt': DateTime.now().toIso8601String(),
        });
      }

      if (query.docs.isNotEmpty) {
        await batch.commit();
        LoggerUtils.logInfo('기존 활성 초대 ${query.docs.length}개 만료 처리', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('기존 초대 만료 처리 실패', tag: _tag, error: e);
      // 실패해도 새 초대 생성은 계속 진행
    }
  }

  /// 초대 코드로 초대 정보 조회
  ///
  /// [invitationCode] 초대 코드
  /// 반환값: 초대 정보 (없으면 null)
  Future<EventInvitation?> getInvitationByCode(String invitationCode) async {
    LoggerUtils.methodStart('getInvitationByCode', tag: _tag, data: {
      'invitationCode': invitationCode,
    });

    try {
      // 인덱스 오류 방지를 위해 단순 쿼리 사용 후 클라이언트에서 필터링
      final query = await _firestore
          .collection('event_invitations')
          .where('invitationCode', isEqualTo: invitationCode.toUpperCase())
          .where('status', isEqualTo: InvitationStatus.pending.value)
          .limit(10) // 동일한 코드가 여러 개 있을 수 있으므로 여유있게 조회
          .get();

      if (query.docs.isEmpty) {
        LoggerUtils.logInfo('초대 코드를 찾을 수 없음: $invitationCode', tag: _tag);
        return null;
      }

      // 클라이언트에서 만료되지 않은 초대 필터링
      final now = DateTime.now();
      for (final doc in query.docs) {
        try {
          final invitation = EventInvitation.fromFirebaseMap(doc.data());

          // 만료되지 않은 초대만 반환
          if (!invitation.isExpired && invitation.expiresAt.isAfter(now)) {
            LoggerUtils.logInfo('초대 정보 조회 완료: ${invitation.eventName}', tag: _tag);
            return invitation;
          }
        } catch (e) {
          LoggerUtils.logWarning('초대 데이터 파싱 실패, 다음 문서로 진행', tag: _tag, error: e);
          continue;
        }
      }

      LoggerUtils.logInfo('유효한 초대 코드를 찾을 수 없음 (모두 만료됨): $invitationCode', tag: _tag);
      return null;
    } catch (e) {
      LoggerUtils.logError('초대 정보 조회 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 초대 코드 검증
  ///
  /// [invitationCode] 초대 코드
  /// [userId] 검증하는 사용자 ID (중복 참여 확인용)
  /// 반환값: 검증 결과 메시지 (성공 시 null)
  Future<String?> validateInvitationCode(String invitationCode, {String? userId}) async {
    LoggerUtils.methodStart('validateInvitationCode', tag: _tag, data: {
      'invitationCode': invitationCode,
      'userId': userId,
    });

    if (invitationCode.trim().isEmpty) {
      return '초대 코드를 입력해주세요';
    }

    if (invitationCode.length != _codeLength) {
      return '초대 코드는 $_codeLength자리여야 합니다';
    }

    final invitation = await getInvitationByCode(invitationCode);
    if (invitation == null) {
      return '유효하지 않은 초대 코드입니다';
    }

    if (invitation.isExpired) {
      return '만료된 초대 코드입니다';
    }

    // 다중 사용자 지원을 위해 상태 검증 로직 변경
    if (invitation.status == InvitationStatus.expired || invitation.status == InvitationStatus.rejected) {
      return '사용할 수 없는 초대 코드입니다';
    }

    // 자신의 행사인지 확인
    final currentUser = _auth.currentUser;
    if (currentUser != null && invitation.ownerUserId == currentUser.uid) {
      return '자신의 행사에는 참여할 수 없습니다';
    }

    // 참여자 수 제한 확인
    if (!invitation.canAcceptMore) {
      return '초대 참여 인원이 가득 찼습니다 (최대 2명)';
    }

    // 중복 참여 확인
    final checkUserId = userId ?? currentUser?.uid;
    if (checkUserId != null && invitation.hasUserAccepted(checkUserId)) {
      return '이미 참여한 초대입니다';
    }

    LoggerUtils.logInfo('초대 코드 검증 성공 (${invitation.acceptedCount}/2)', tag: _tag);
    return null; // 검증 성공
  }

  /// 초대 만료 시간 계산
  ///
  /// [createdAt] 초대 생성 시간
  /// 반환값: 만료 시간
  DateTime calculateExpiryTime(DateTime createdAt) {
    return createdAt.add(_invitationExpiry);
  }

  /// 초대가 만료되었는지 확인
  ///
  /// [createdAt] 초대 생성 시간
  /// 반환값: 만료 여부
  bool isInvitationExpired(DateTime createdAt) {
    final expiryTime = calculateExpiryTime(createdAt);
    return DateTime.now().isAfter(expiryTime);
  }

  /// 만료된 초대들 정리 (상태 변경 + 실제 삭제)
  ///
  /// 반환값: 정리된 초대 수
  Future<int> cleanupExpiredInvitations() async {
    LoggerUtils.methodStart('cleanupExpiredInvitations', tag: _tag);

    try {
      final now = DateTime.now();

      // 1. 만료 시간이 지난 초대들 조회 (expiresAt 기준)
      final expiredQuery = await _firestore
          .collection('event_invitations')
          .where('expiresAt', isLessThan: now.toIso8601String())
          .get();

      // 2. 오래된 만료 상태 초대들 조회 (1일 이상 된 것들은 삭제)
      final oldExpiredQuery = await _firestore
          .collection('event_invitations')
          .where('status', isEqualTo: InvitationStatus.expired.value)
          .where('respondedAt', isLessThan: now.subtract(const Duration(days: 1)).toIso8601String())
          .get();

      int totalCleaned = 0;

      if (expiredQuery.docs.isNotEmpty) {
        // 만료된 초대들의 상태를 expired로 변경
        final batch1 = _firestore.batch();
        for (final doc in expiredQuery.docs) {
          batch1.update(doc.reference, {
            'status': InvitationStatus.expired.value,
            'respondedAt': now.toIso8601String(),
          });
        }
        await batch1.commit();
        totalCleaned += expiredQuery.docs.length;
        LoggerUtils.logInfo('만료된 초대 ${expiredQuery.docs.length}개 상태 변경 완료', tag: _tag);
      }

      if (oldExpiredQuery.docs.isNotEmpty) {
        // 오래된 만료 초대들 실제 삭제
        final batch2 = _firestore.batch();
        for (final doc in oldExpiredQuery.docs) {
          batch2.delete(doc.reference);
        }
        await batch2.commit();
        LoggerUtils.logInfo('오래된 만료 초대 ${oldExpiredQuery.docs.length}개 삭제 완료', tag: _tag);
      }

      if (totalCleaned == 0) {
        LoggerUtils.logInfo('정리할 만료된 초대가 없음', tag: _tag);
      } else {
        LoggerUtils.logInfo('총 ${totalCleaned}개의 만료된 초대 정리 완료', tag: _tag);
      }

      return totalCleaned;
    } catch (e) {
      LoggerUtils.logError('만료된 초대 정리 실패', tag: _tag, error: e);
      
      rethrow;
    }
  }

  /// 자동 정리 실행 (앱 시작 시, 초대 관련 작업 시 호출)
  Future<void> autoCleanupExpiredInvitations() async {
    try {
      // 백그라운드에서 실행하여 사용자 경험에 영향을 주지 않음
      unawaited(cleanupExpiredInvitations());
    } catch (e) {
      LoggerUtils.logWarning('자동 정리 실행 중 오류 (무시됨)', tag: _tag, error: e);
    }
  }
}
