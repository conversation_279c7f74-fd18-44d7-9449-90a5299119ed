/// 바라 부스 매니저 - 사용자 설정 동기화 서비스
///
/// 사용자별 설정 정보의 Firebase 동기화를 담당하는 서비스입니다.
/// - 마지막 선택된 워크스페이스 ID 동기화
/// - 기타 사용자 개인 설정 동기화
/// - 실시간 동기화 지원
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 7월
library;

import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_settings.dart';
import '../utils/logger_utils.dart';
import 'subscription_service.dart';
import '../models/subscription_plan.dart';

/// 사용자 설정 동기화 서비스
class UserSettingsService {
  static const String _tag = 'UserSettingsService';
  
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 현재 로그인된 사용자 ID
  String? get _currentUserId => _auth.currentUser?.uid;

  /// 사용자가 로그인되어 있는지 확인
  bool get isUserLoggedIn => _currentUserId != null;

  /// 사용자 설정을 Firebase에 업로드
  Future<void> uploadUserSettings(UserSettings settings) async {
    try {
      if (!isUserLoggedIn) {
        LoggerUtils.logInfo('로그인되지 않은 상태 - 사용자 설정 업로드 건너뛰기', tag: _tag);
        return;
      }

      // 프로플랜에서는 사용자 설정을 항상 동기화 (행사별 동기화 상태와 무관)
      final subscriptionService = SubscriptionService();
      final planType = await subscriptionService.getCurrentPlanType();

      if (planType != SubscriptionPlanType.pro) {
        LoggerUtils.logInfo('프리/플러스 플랜에서는 사용자 설정 업로드를 건너뜁니다', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('사용자 설정 업로드 시작: lastWorkspaceId=${settings.lastWorkspaceId}', tag: _tag);

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('settings')
          .doc('user_settings')
          .set(settings.toFirebaseMap(), SetOptions(merge: true));

      LoggerUtils.logInfo('사용자 설정 업로드 완료', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('사용자 설정 업로드 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Firebase에서 사용자 설정 다운로드
  Future<UserSettings?> downloadUserSettings() async {
    try {
      if (!isUserLoggedIn) {
        LoggerUtils.logInfo('로그인되지 않은 상태 - 사용자 설정 다운로드 건너뛰기', tag: _tag);
        return null;
      }

      // 프로플랜에서는 사용자 설정을 항상 동기화 (행사별 동기화 상태와 무관)
      final subscriptionService = SubscriptionService();
      final planType = await subscriptionService.getCurrentPlanType();

      if (planType != SubscriptionPlanType.pro) {
        LoggerUtils.logInfo('프리/플러스 플랜에서는 사용자 설정 다운로드를 건너뜁니다', tag: _tag);
        return null;
      }

      LoggerUtils.logInfo('사용자 설정 다운로드 시작', tag: _tag);

      final doc = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('settings')
          .doc('user_settings')
          .get();

      if (!doc.exists || doc.data() == null) {
        LoggerUtils.logInfo('저장된 사용자 설정이 없습니다', tag: _tag);
        return null;
      }

      final settings = UserSettingsExtensions.fromFirebaseMap(doc.data()!);
      LoggerUtils.logInfo('사용자 설정 다운로드 완료: lastWorkspaceId=${settings.lastWorkspaceId}', tag: _tag);
      
      return settings;
    } catch (e, stackTrace) {
      LoggerUtils.logError('사용자 설정 다운로드 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 마지막 워크스페이스 ID만 업데이트
  Future<void> updateLastWorkspaceId(int? workspaceId, {String? deviceId}) async {
    try {
      if (!isUserLoggedIn) {
        LoggerUtils.logWarning('로그인되지 않아 워크스페이스 ID 업데이트를 건너뜁니다', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('마지막 워크스페이스 ID 업데이트: $workspaceId', tag: _tag);

      // 기존 설정 가져오기 (없으면 기본값 사용)
      UserSettings currentSettings;
      try {
        currentSettings = await downloadUserSettings() ?? UserSettings.createDefault();
      } catch (e) {
        LoggerUtils.logWarning('기존 설정 로드 실패, 기본값 사용', tag: _tag, error: e);
        currentSettings = UserSettings.createDefault();
      }

      // 워크스페이스 ID 업데이트
      final updatedSettings = currentSettings.withWorkspaceUpdate(
        workspaceId: workspaceId,
        deviceId: deviceId,
      );

      await uploadUserSettings(updatedSettings);
      LoggerUtils.logInfo('마지막 워크스페이스 ID 업데이트 완료: $workspaceId', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('마지막 워크스페이스 ID 업데이트 실패', tag: _tag, error: e, stackTrace: stackTrace);
      // 실패해도 로컬 동작에는 영향을 주지 않도록 예외를 다시 던지지 않음
    }
  }

  /// 실시간 동기화 설정 업데이트
  Future<void> updateRealtimeSyncSetting({
    required bool enabled,
    String? deviceId,
  }) async {
    try {
      if (!isUserLoggedIn) {
        LoggerUtils.logInfo('로그인되지 않은 상태 - 실시간 동기화 설정 업데이트 건너뛰기', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('실시간 동기화 설정 업데이트 시작: enabled=$enabled', tag: _tag);

      // 현재 설정 로드
      UserSettings currentSettings;
      try {
        currentSettings = await downloadUserSettings() ?? UserSettings.createDefault();
      } catch (e) {
        LoggerUtils.logWarning('기존 설정 로드 실패, 기본값 사용', tag: _tag, error: e);
        currentSettings = UserSettings.createDefault();
      }

      // 실시간 동기화 설정 업데이트
      final updatedSettings = currentSettings.withRealtimeSyncUpdate(
        enabled: enabled,
        deviceId: deviceId,
      );

      await uploadUserSettings(updatedSettings);
      LoggerUtils.logInfo('실시간 동기화 설정 업데이트 완료: enabled=$enabled', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('실시간 동기화 설정 업데이트 실패', tag: _tag, error: e, stackTrace: stackTrace);
      rethrow; // 이 설정은 중요하므로 예외를 다시 던짐
    }
  }

  /// 사용자 설정 실시간 구독 (읽기 사용량 최적화를 위해 비활성화)
  /// 필요할 때만 downloadUserSettings()를 사용하도록 변경
  Stream<UserSettings?> watchUserSettings() {
    LoggerUtils.logInfo('실시간 구독 대신 필요할 때만 설정을 읽도록 최적화됨', tag: _tag);
    return Stream.value(null);
  }

  /// 사용자 설정을 한 번만 읽기 (실시간 구독 대신)
  Future<UserSettings?> getUserSettings() async {
    if (!isUserLoggedIn) {
      return null;
    }

    try {
      // 프리/플러스 플랜에서는 사용자 설정을 서버에서 읽지 않음 (POS는 오프라인 우선)
      final subscriptionService = SubscriptionService();
      final planType = await subscriptionService.getCurrentPlanType();
      final canUseRealtimeSync = planType == SubscriptionPlanType.pro;

      if (!canUseRealtimeSync) {
        LoggerUtils.logInfo('프리/플러스 플랜에서는 사용자 설정 읽기를 건너뜁니다', tag: _tag);
        return null;
      }

      final doc = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('settings')
          .doc('user_settings')
          .get();

      if (!doc.exists || doc.data() == null) {
        return null;
      }

      return UserSettingsExtensions.fromFirebaseMap(doc.data()!);
    } catch (e) {
      LoggerUtils.logError('사용자 설정 읽기 실패', tag: _tag, error: e);
      return null;
    }
  }
}

/// UserSettingsService Provider
final userSettingsServiceProvider = Provider<UserSettingsService>((ref) {
  return UserSettingsService();
});
