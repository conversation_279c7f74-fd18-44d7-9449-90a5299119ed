# 🚀 바라 부스 매니저 - 실시간 동기화 시스템 개선 가이드

## 📋 프로젝트 개요

### 현재 상황
- **프로젝트명**: 바라 부스 매니저 (Parabara)
- **개선 목표**: 실시간 동기화 시스템을 행사 단위로 재설계
- **핵심 문제**: 플랜 기반 동기화와 초대 시스템 간의 충돌
- **예상 효과**: 서버 비용 60-80% 절약, 앱 성능 대폭 향상

### 핵심 변경사항
1. **사용자 단위** → **행사 단위** 실시간 동기화 제어
2. **전체 데이터** → **선택적 데이터** 동기화
3. **초대 인원** 3명 → 2명으로 축소
4. **프로 플랜만** 행사별 동기화 설정 권한 부여

---

## ⚠️ 핵심 문제점 분석

### 1. 현재 시스템의 한계
```
문제 1: 플랜 기반 제한
- 프리/플러스 플랜 사용자가 초대받아도 실시간 데이터 볼 수 없음
- 프로 플랜 사용자가 동기화 OFF 시 초대받은 사용자도 영향받음

문제 2: 비효율적 리소스 사용
- 모든 행사 데이터를 실시간으로 동기화
- 사용하지 않는 행사 데이터도 지속적으로 업데이트
- Firebase 읽기/쓰기 사용량 과다

문제 3: 초대 시스템과의 불일치
- 초대 시스템은 권한 기반이지만 동기화는 플랜 기반
- 초대받은 행사 데이터는 볼 수 있지만 실시간 업데이트 안됨
```

### 2. 사용자 경험 문제
- 초대받은 사용자의 제한적인 협업 경험 (실시간 동기화 불가)

---

## 💡 해결 방안: 행사 단위 실시간 동기화

### 핵심 아이디어
```
기존: 사용자별 전역 ON/OFF (플랜 기반)
신규: 행사별 개별 ON/OFF + 권한 기반 제어

구조:
- 프로 플랜: 행사별 동기화 설정 가능
- 프리/플러스: 읽기 전용 (항상 OFF)
- 초대받은 사용자: 소유자 설정에 따라 동기화
```

### 예상 효과
- **초대 시스템 완성**: 초대받은 사용자도 실시간 협업 가능
- **유연한 제어**: 필요한 행사만 실시간 동기화
- **비즈니스 모델 강화**: 프로 플랜 차별화 명확

---

## 🗺️ 상세 로드맵

### ✅ **이미 구현된 기능들**
- **행사별 데이터 로딩**: 현재 워크스페이스 기반으로 해당 행사 데이터만 로드
- **현재 행사만 실시간 구독**: `subscribeToEvent(eventId)`로 특정 행사만 구독
- **델타 동기화**: `smartSync()`로 서버 데이터 유무 확인 후 필요시만 동기화
- **로컬 중심 아키텍처**: SQLite 기반으로 모든 데이터 로컬 저장

### 1단계: Event 모델 확장 (3일)
**목표**: 행사별 동기화 설정을 위한 데이터 모델 확장

#### 작업 항목
- [ ] Event 모델에 실시간 동기화 필드 추가
  - `realtimeSyncEnabled: bool`
  - `syncEnabledByUserId: String?`
  - `syncSettingUpdatedAt: DateTime?`
- [ ] 데이터베이스 마이그레이션 스크립트 작성
- [ ] Firebase 구조 업데이트

#### 변경 파일
- `lib/models/event.dart`
- `lib/services/database_service.dart`

#### 완료 기준
- [ ] Event 모델 필드 추가 완료
- [ ] 데이터 마이그레이션 오류 없음
- [ ] 기존 기능 정상 작동 확인

**진행도**: ⬜ 0% (미시작)

---

### 2단계: 권한 기반 동기화 제어 (4일)
**목표**: 행사별 권한에 따른 실시간 동기화 제어 로직 구현

#### 작업 항목
- [ ] 행사별 동기화 권한 체크 로직
- [ ] 초대받은 사용자의 동기화 권한 처리
- [ ] 소유자 설정 변경 감지 시스템

#### 변경 파일
- `lib/services/realtime_sync_service_main.dart`
- `lib/services/data_sync_service.dart`

#### 완료 기준
- [ ] 권한 기반 접근 제어 정상 작동
- [ ] 초대받은 사용자 실시간 동기화 확인
- [ ] 소유자 설정 변경 시 즉시 반영

**진행도**: ⬜ 0% (미시작)

---

### 3단계: UI 및 초대 시스템 개선 (3일)
**목표**: 행사별 동기화 설정 UI 및 초대 시스템 개선

#### 작업 항목
- [ ] 행사별 동기화 설정 UI 추가
- [ ] 프로 플랜 권한 체크 UI
- [ ] 초대 인원 3명 → 2명으로 축소
- [ ] 받은 초대 관리 UI (초대 코드 입력 다이얼로그에 통합)

#### 변경 파일
- `lib/screens/event/event_list_screen.dart` (설정 UI 추가)
- `lib/models/event_invitation.dart` (인원 축소)
- `lib/widgets/invitation_dialog.dart` (받은 초대 관리 통합)

#### 완료 기준
- [ ] 프로 플랜 사용자만 설정 변경 가능
- [ ] 초대 시스템 정상 작동 확인
- [ ] 받은 초대 관리 기능 완료

**진행도**: ⬜ 0% (미시작)

---

## 📏 작업 규칙 및 주의사항

### 🔒 기존 기능 보호 규칙
1. **하위 호환성 유지**
   - 기존 사용자 데이터 손실되어도 됨. (서비스 중 아님)
   - 기존 API 호환성 유지
   - 마이그레이션은 필요없음 (서비스 중 아님)

2. **기능 영향 최소화**
   - 새로운 기능은 기본값으로 비활성화
   - 기존 워크플로우 변경 금지


### 🧪 코드 품질 규칙

1. **코드 리뷰**
   - 모든 변경사항 리뷰 필수
   - 성능 영향 분석 포함
   - 보안 취약점 점검

2. **문서화**
   - API 변경사항 문서화
   - 사용자 가이드 업데이트
   - 개발자 문서 최신화

### 🔍 성능 모니터링
1. **Firebase 사용량**
   - 읽기/쓰기 횟수 모니터링
   - 비용 변화 추적


---

## 🔧 기술적 세부사항

### 새로 추가할 서비스
```dart
// 행사 소유자 설정 관리
class EventOwnerSettingsService {
  Future<bool> isRealtimeSyncEnabled(int eventId);
  Stream<bool> watchOwnerRealtimeSyncSetting(int eventId);
  Future<void> updateRealtimeSyncSetting(int eventId, bool enabled);
}

// 선택적 데이터 로더
class SelectiveDataLoader {
  Future<void> loadEssentialData();
  Future<void> loadEventData(int eventId);
  Future<void> mergeDeltaChanges(Map local, Map server);
}

// 스마트 캐시 관리
class SmartCacheService {
  Future<void> cacheEventData(int eventId, Map data);
  Future<Map?> getCachedEventData(int eventId);
  Future<void> clearOldCache();
}
```

### Firebase 구조 변경
```
기존:
users/{userId}/settings/user_settings
  - realtimeSyncEnabled: bool (전역)

추가:
users/{userId}/events/{eventId}
  - realtimeSyncEnabled: bool (행사별)
  - syncEnabledByUserId: string
  - syncSettingUpdatedAt: timestamp
```

---

## ⚠️ 위험 요소 및 대응 방안

### 높은 위험도

3. **Firebase 비용 증가**
   - 위험: 예상과 다른 사용량 패턴
   - 대응: 실시간 모니터링, 사용량 제한, 비용 알림

### 중간 위험도

2. **권한 오류**
   - 위험: 잘못된 권한 설정으로 인한 접근 제한
   - 대응: 권한 검증 로직 강화, 오류 로깅

### 낮은 위험도
1. **UI/UX 개선 필요**
   - 위험: 사용자 경험 저하
   - 대응: 사용자 테스트, 피드백 수집, 지속적 개선

---

## 📊 진행도 추적

### 전체 진행률
```
전체 진행도: ⬜⬜⬜ 0% (0/3 단계 완료)

1단계 (Event 모델 확장): ⬜⬜⬜⬜⬜ 0%
2단계 (권한 기반 제어): ⬜⬜⬜⬜⬜ 0%
3단계 (UI 및 초대 개선): ⬜⬜⬜⬜⬜ 0%
```

### 주요 마일스톤
- [ ] **M1**: Event 모델 확장 완료 (3일)
- [ ] **M2**: 권한 기반 동기화 제어 완료 (7일)
- [ ] **M3**: UI 및 초대 시스템 개선 완료 (10일)

### 성과 지표
- **기능**: 초대받은 사용자 실시간 협업 가능
- **사용자**: 행사별 유연한 동기화 제어
- **비즈니스**: 프로 플랜 차별화 강화

---

## 📝 작업 로그

### 2025-08-25
- [x] 프로젝트 분석 및 로드맵 수립 완료
- [x] UP_GUIDE.md 문서 작성 완료
- [ ] 1단계 작업 시작 예정

---

## 🔗 참고 자료

### 관련 문서
- [ARCHITECTURE.md](./ARCHITECTURE.md) - 전체 시스템 아키텍처
- [API.md](./API.md) - API 명세서
- [DEPLOYMENT.md](./DEPLOYMENT.md) - 배포 가이드

### 외부 참고
- [Firebase 실시간 동기화 베스트 프랙티스](https://firebase.google.com/docs/firestore/best-practices)
- [Flutter 성능 최적화 가이드](https://docs.flutter.dev/perf)
- [모바일 앱 캐싱 전략](https://developer.android.com/topic/performance/caching)

---

**📌 중요**: 이 문서는 작업 진행에 따라 지속적으로 업데이트됩니다. 모든 변경사항은 작업 로그에 기록해주세요.