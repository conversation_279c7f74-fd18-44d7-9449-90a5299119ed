/// 새로운 로그아웃 매니저 - Phoenix를 사용한 완전한 앱 재시작
/// 
/// 기존 로그아웃의 문제점들을 해결하기 위해 새로 설계된 로그아웃 시스템
/// - AppWrapper 중간 이동 문제 해결
/// - 상태 동기화 문제 해결
/// - 완전한 앱 초기화 보장

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';

import '../utils/logger_utils.dart';
import '../app/cleanup.dart';
import '../providers/nickname_provider.dart';
import '../providers/realtime_sync_provider.dart';

class LogoutManager {
  static const String _tag = 'LogoutManager';

  /// 완전한 로그아웃 수행 - Phoenix를 사용한 앱 재시작
  /// 
  /// 이 방법은 다음과 같은 장점이 있습니다:
  /// 1. AppWrapper 중간 이동 문제 완전 해결
  /// 2. 모든 상태와 메모리 완전 초기화
  /// 3. 복잡한 상태 동기화 로직 불필요
  /// 4. 확실한 로그아웃 보장
  static Future<void> performCompleteLogout({
    required BuildContext context,
    required WidgetRef ref,
  }) async {
    try {
      LoggerUtils.logInfo('🔄 새로운 로그아웃 시작', tag: _tag);

      // 1. 로딩 다이얼로그 표시
      _showLogoutDialog(context);

      // 2. 백그라운드에서 데이터 정리 (UI 블로킹 방지)
      await _performDataCleanup(ref);

      // 3. Firebase 로그아웃 (서버 데이터는 유지)
      await FirebaseAuth.instance.signOut();
      LoggerUtils.logInfo('✅ Firebase 로그아웃 완료 (서버 닉네임 데이터 유지)', tag: _tag);

      // 4. SharedPreferences 완전 초기화
      await _resetSharedPreferences();

      // 5. 잠시 대기 (정리 작업 완료 보장)
      await Future.delayed(const Duration(milliseconds: 500));

      // 6. 앱 완전 재시작 (Phoenix)
      LoggerUtils.logInfo('🔄 앱 완전 재시작 시작', tag: _tag);
      
      // 다이얼로그 닫기
      if (context.mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      
      // Phoenix를 사용한 앱 완전 재시작
      if (context.mounted) {
        Phoenix.rebirth(context);
      }

    } catch (e) {
      LoggerUtils.logError('❌ 로그아웃 중 오류 발생', tag: _tag, error: e);
      
      // 오류 발생 시에도 강제 재시작
      if (context.mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        Phoenix.rebirth(context);
      }
    }
  }

  /// 로딩 다이얼로그 표시
  static void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false, // 뒤로가기 방지
        child: const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('로그아웃 중...'),
            ],
          ),
        ),
      ),
    );
  }

  /// 데이터 정리 작업
  static Future<void> _performDataCleanup(WidgetRef ref) async {
    try {
      LoggerUtils.logInfo('🗑️ 데이터 정리 시작', tag: _tag);

      // 1. Provider 상태 완전 정리 (iOS 상태 불일치 문제 해결)
      await _cleanupProviderStates(ref);
      LoggerUtils.logInfo('✅ Provider 상태 정리 완료', tag: _tag);

      // 2. 앱 리소스 정리
      await cleanupAppResources();
      LoggerUtils.logInfo('✅ 앱 리소스 정리 완료', tag: _tag);

      // 3. 로컬 데이터 정리 (provider 상태 변경 없이)
      await _cleanupLocalDataSilently();
      LoggerUtils.logInfo('✅ 로컬 데이터 정리 완료', tag: _tag);

      // 4. 메모리 정리 (iOS에서 중요)
      await _performMemoryCleanup();
      LoggerUtils.logInfo('✅ 메모리 정리 완료', tag: _tag);

    } catch (e) {
      LoggerUtils.logError('❌ 데이터 정리 중 오류', tag: _tag, error: e);
      // 오류가 있어도 계속 진행
    }
  }

  /// Provider 상태 완전 정리
  static Future<void> _cleanupProviderStates(WidgetRef ref) async {
    try {
      LoggerUtils.logInfo('Provider 상태 정리 시작', tag: _tag);

      // 닉네임 Provider 정리
      try {
        ref.read(nicknameProvider.notifier).clearNickname();
        LoggerUtils.logInfo('닉네임 Provider 정리 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('닉네임 Provider 정리 실패', tag: _tag, error: e);
      }

      // 다른 주요 Provider들도 정리
      try {
        // 실시간 동기화 서비스 정리
        final realtimeSyncService = ref.read(realtimeSyncServiceProvider);
        await realtimeSyncService.dispose();
        LoggerUtils.logInfo('실시간 동기화 서비스 정리 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('실시간 동기화 서비스 정리 실패 - 계속 진행', tag: _tag, error: e);
        // 서비스 정리 실패해도 로그아웃은 계속 진행
      }

      // 추가 대기 시간 (iOS에서 상태 정리 완료 보장)
      await Future.delayed(const Duration(milliseconds: 300));

    } catch (e) {
      LoggerUtils.logError('Provider 상태 정리 실패', tag: _tag, error: e);
    }
  }

  /// 메모리 정리 (iOS 최적화)
  static Future<void> _performMemoryCleanup() async {
    try {
      LoggerUtils.logInfo('메모리 정리 시작', tag: _tag);

      // 가비지 컬렉션 유도 (iOS에서 중요)
      for (int i = 0; i < 3; i++) {
        await Future.delayed(const Duration(milliseconds: 100));
        // 메모리 정리를 위한 대기
      }

      LoggerUtils.logInfo('메모리 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('메모리 정리 실패', tag: _tag, error: e);
    }
  }

  /// 조용한 로컬 데이터 정리 (provider 상태 변경 없이)
  static Future<void> _cleanupLocalDataSilently() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 중요한 상태들 백업 (iOS에서 더 안전한 처리)
      final isOnboarded = prefs.getBool('isOnboarded') ?? false;
      final allKeys = prefs.getKeys().toList();

      LoggerUtils.logInfo('SharedPreferences 정리 시작 - 총 ${allKeys.length}개 키', tag: _tag);

      // 모든 데이터 삭제 (iOS에서 더 확실한 정리)
      for (final key in allKeys) {
        try {
          await prefs.remove(key);
        } catch (e) {
          LoggerUtils.logWarning('키 삭제 실패: $key', tag: _tag, error: e);
        }
      }

      // 추가 확인을 위한 clear() 호출
      await prefs.clear();

      // 필요한 상태만 복원 (로그아웃 시에는 온보딩을 다시 보여주지 않음)
      await prefs.setBool('isOnboarded', isOnboarded);
      await prefs.setString('auth_mode', 'login');

      // iOS에서 상태 불일치 방지를 위한 추가 플래그
      await prefs.setBool('logout_completed', true);

      LoggerUtils.logInfo('SharedPreferences 완전 정리 완료 (온보딩 상태 유지: $isOnboarded)', tag: _tag);

      // SQLite 데이터베이스 정리
      await _cleanupDatabaseSilently();

      // 캐시 정리
      await _cleanupCachesSilently();

    } catch (e) {
      LoggerUtils.logError('조용한 데이터 정리 실패', tag: _tag, error: e);
    }
  }

  /// 데이터베이스 조용히 정리
  static Future<void> _cleanupDatabaseSilently() async {
    try {
      LoggerUtils.logInfo('SQLite 데이터베이스 정리 중...', tag: _tag);

      // 데이터베이스 파일 직접 삭제
      final databasesPath = await getDatabasesPath();
      final dbPath = '$databasesPath/parabara_database.db';
      final dbFile = File(dbPath);

      if (await dbFile.exists()) {
        await dbFile.delete();
        LoggerUtils.logInfo('SQLite 데이터베이스 파일 삭제 완료: $dbPath', tag: _tag);
      } else {
        LoggerUtils.logInfo('SQLite 데이터베이스 파일이 존재하지 않음: $dbPath', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('데이터베이스 정리 실패', tag: _tag, error: e);
    }
  }

  /// 캐시 조용히 정리
  static Future<void> _cleanupCachesSilently() async {
    try {
      LoggerUtils.logInfo('캐시 정리 중...', tag: _tag);

      // 이미지 캐시 디렉토리 정리
      try {
        final appDir = await getApplicationDocumentsDirectory();
        final imageDir = Directory('${appDir.path}/product_images');
        if (await imageDir.exists()) {
          await imageDir.delete(recursive: true);
          LoggerUtils.logInfo('이미지 캐시 디렉토리 삭제 완료', tag: _tag);
        }
      } catch (e) {
        LoggerUtils.logWarning('이미지 캐시 정리 실패 (무시)', tag: _tag, error: e);
      }

      // 임시 파일 정리
      try {
        final tempDir = await getTemporaryDirectory();
        final tempFiles = await tempDir.list().toList();
        for (final file in tempFiles) {
          if (file is File) {
            await file.delete();
          }
        }
        LoggerUtils.logInfo('임시 파일 정리 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logWarning('임시 파일 정리 실패 (무시)', tag: _tag, error: e);
      }

    } catch (e) {
      LoggerUtils.logError('캐시 정리 실패', tag: _tag, error: e);
    }
  }

  /// SharedPreferences 완전 초기화 (로그아웃용 - 온보딩 상태 유지)
  static Future<void> _resetSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 온보딩 상태 백업
      final isOnboarded = prefs.getBool('isOnboarded') ?? false;

      // 모든 데이터 삭제
      await prefs.clear();

      // 온보딩 상태 복원 (로그아웃 시에는 온보딩을 다시 보여주지 않음)
      await prefs.setBool('isOnboarded', isOnboarded);
      await prefs.setString('auth_mode', 'login');

      LoggerUtils.logInfo('✅ SharedPreferences 초기화 완료 (온보딩 상태 유지: $isOnboarded)', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('SharedPreferences 초기화 실패', tag: _tag, error: e);
    }
  }

  // 🔥 로그아웃 시 Firebase 사용자 설정 삭제 메서드 제거
  // 로그아웃은 로컬 인증 상태만 해제하고, 서버의 사용자 데이터(닉네임 등)는 유지해야 함
  // 회원탈퇴 시에만 서버 데이터를 삭제해야 함 (MyPageScreen._clearFirebaseUserSettingsForDeletion 참조)
}
