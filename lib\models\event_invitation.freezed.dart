// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'event_invitation.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$EventInvitation {

/// 초대 고유 ID
 String get id;/// 초대 코드 (6자리 영숫자)
 String get invitationCode;/// 행사 ID
 int get eventId;/// 행사명 (캐시용)
 String get eventName;/// 행사 소유자 사용자 ID
 String get ownerUserId;/// 행사 소유자 닉네임 (캐시용)
 String get ownerNickname;/// 초대받은 사용자 ID (수락 후 설정됨) - 하위 호환성을 위해 유지
 String? get invitedUserId;/// 초대받은 사용자 닉네임 (수락 후 설정됨) - 하위 호환성을 위해 유지
 String? get invitedNickname;/// 초대를 수락한 사용자들 목록 (최대 2명) - Firebase에서만 사용
 List<InvitedUser> get acceptedUsers;/// 초대 상태
 InvitationStatus get status;/// 초대 생성 시간
 DateTime get createdAt;/// 초대 만료 시간 (생성 후 2시간)
 DateTime get expiresAt;/// 초대 응답 시간 (수락/거절 시 설정됨)
 DateTime? get respondedAt;
/// Create a copy of EventInvitation
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EventInvitationCopyWith<EventInvitation> get copyWith => _$EventInvitationCopyWithImpl<EventInvitation>(this as EventInvitation, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EventInvitation&&(identical(other.id, id) || other.id == id)&&(identical(other.invitationCode, invitationCode) || other.invitationCode == invitationCode)&&(identical(other.eventId, eventId) || other.eventId == eventId)&&(identical(other.eventName, eventName) || other.eventName == eventName)&&(identical(other.ownerUserId, ownerUserId) || other.ownerUserId == ownerUserId)&&(identical(other.ownerNickname, ownerNickname) || other.ownerNickname == ownerNickname)&&(identical(other.invitedUserId, invitedUserId) || other.invitedUserId == invitedUserId)&&(identical(other.invitedNickname, invitedNickname) || other.invitedNickname == invitedNickname)&&const DeepCollectionEquality().equals(other.acceptedUsers, acceptedUsers)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.respondedAt, respondedAt) || other.respondedAt == respondedAt));
}


@override
int get hashCode => Object.hash(runtimeType,id,invitationCode,eventId,eventName,ownerUserId,ownerNickname,invitedUserId,invitedNickname,const DeepCollectionEquality().hash(acceptedUsers),status,createdAt,expiresAt,respondedAt);

@override
String toString() {
  return 'EventInvitation(id: $id, invitationCode: $invitationCode, eventId: $eventId, eventName: $eventName, ownerUserId: $ownerUserId, ownerNickname: $ownerNickname, invitedUserId: $invitedUserId, invitedNickname: $invitedNickname, acceptedUsers: $acceptedUsers, status: $status, createdAt: $createdAt, expiresAt: $expiresAt, respondedAt: $respondedAt)';
}


}

/// @nodoc
abstract mixin class $EventInvitationCopyWith<$Res>  {
  factory $EventInvitationCopyWith(EventInvitation value, $Res Function(EventInvitation) _then) = _$EventInvitationCopyWithImpl;
@useResult
$Res call({
 String id, String invitationCode, int eventId, String eventName, String ownerUserId, String ownerNickname, String? invitedUserId, String? invitedNickname, List<InvitedUser> acceptedUsers, InvitationStatus status, DateTime createdAt, DateTime expiresAt, DateTime? respondedAt
});




}
/// @nodoc
class _$EventInvitationCopyWithImpl<$Res>
    implements $EventInvitationCopyWith<$Res> {
  _$EventInvitationCopyWithImpl(this._self, this._then);

  final EventInvitation _self;
  final $Res Function(EventInvitation) _then;

/// Create a copy of EventInvitation
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? invitationCode = null,Object? eventId = null,Object? eventName = null,Object? ownerUserId = null,Object? ownerNickname = null,Object? invitedUserId = freezed,Object? invitedNickname = freezed,Object? acceptedUsers = null,Object? status = null,Object? createdAt = null,Object? expiresAt = null,Object? respondedAt = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,invitationCode: null == invitationCode ? _self.invitationCode : invitationCode // ignore: cast_nullable_to_non_nullable
as String,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,eventName: null == eventName ? _self.eventName : eventName // ignore: cast_nullable_to_non_nullable
as String,ownerUserId: null == ownerUserId ? _self.ownerUserId : ownerUserId // ignore: cast_nullable_to_non_nullable
as String,ownerNickname: null == ownerNickname ? _self.ownerNickname : ownerNickname // ignore: cast_nullable_to_non_nullable
as String,invitedUserId: freezed == invitedUserId ? _self.invitedUserId : invitedUserId // ignore: cast_nullable_to_non_nullable
as String?,invitedNickname: freezed == invitedNickname ? _self.invitedNickname : invitedNickname // ignore: cast_nullable_to_non_nullable
as String?,acceptedUsers: null == acceptedUsers ? _self.acceptedUsers : acceptedUsers // ignore: cast_nullable_to_non_nullable
as List<InvitedUser>,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as InvitationStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,respondedAt: freezed == respondedAt ? _self.respondedAt : respondedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [EventInvitation].
extension EventInvitationPatterns on EventInvitation {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EventInvitation value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EventInvitation() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EventInvitation value)  $default,){
final _that = this;
switch (_that) {
case _EventInvitation():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EventInvitation value)?  $default,){
final _that = this;
switch (_that) {
case _EventInvitation() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String invitationCode,  int eventId,  String eventName,  String ownerUserId,  String ownerNickname,  String? invitedUserId,  String? invitedNickname,  List<InvitedUser> acceptedUsers,  InvitationStatus status,  DateTime createdAt,  DateTime expiresAt,  DateTime? respondedAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EventInvitation() when $default != null:
return $default(_that.id,_that.invitationCode,_that.eventId,_that.eventName,_that.ownerUserId,_that.ownerNickname,_that.invitedUserId,_that.invitedNickname,_that.acceptedUsers,_that.status,_that.createdAt,_that.expiresAt,_that.respondedAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String invitationCode,  int eventId,  String eventName,  String ownerUserId,  String ownerNickname,  String? invitedUserId,  String? invitedNickname,  List<InvitedUser> acceptedUsers,  InvitationStatus status,  DateTime createdAt,  DateTime expiresAt,  DateTime? respondedAt)  $default,) {final _that = this;
switch (_that) {
case _EventInvitation():
return $default(_that.id,_that.invitationCode,_that.eventId,_that.eventName,_that.ownerUserId,_that.ownerNickname,_that.invitedUserId,_that.invitedNickname,_that.acceptedUsers,_that.status,_that.createdAt,_that.expiresAt,_that.respondedAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String invitationCode,  int eventId,  String eventName,  String ownerUserId,  String ownerNickname,  String? invitedUserId,  String? invitedNickname,  List<InvitedUser> acceptedUsers,  InvitationStatus status,  DateTime createdAt,  DateTime expiresAt,  DateTime? respondedAt)?  $default,) {final _that = this;
switch (_that) {
case _EventInvitation() when $default != null:
return $default(_that.id,_that.invitationCode,_that.eventId,_that.eventName,_that.ownerUserId,_that.ownerNickname,_that.invitedUserId,_that.invitedNickname,_that.acceptedUsers,_that.status,_that.createdAt,_that.expiresAt,_that.respondedAt);case _:
  return null;

}
}

}

/// @nodoc


class _EventInvitation implements EventInvitation {
  const _EventInvitation({required this.id, required this.invitationCode, required this.eventId, required this.eventName, required this.ownerUserId, required this.ownerNickname, this.invitedUserId, this.invitedNickname, final  List<InvitedUser> acceptedUsers = const [], this.status = InvitationStatus.pending, required this.createdAt, required this.expiresAt, this.respondedAt}): _acceptedUsers = acceptedUsers;
  

/// 초대 고유 ID
@override final  String id;
/// 초대 코드 (6자리 영숫자)
@override final  String invitationCode;
/// 행사 ID
@override final  int eventId;
/// 행사명 (캐시용)
@override final  String eventName;
/// 행사 소유자 사용자 ID
@override final  String ownerUserId;
/// 행사 소유자 닉네임 (캐시용)
@override final  String ownerNickname;
/// 초대받은 사용자 ID (수락 후 설정됨) - 하위 호환성을 위해 유지
@override final  String? invitedUserId;
/// 초대받은 사용자 닉네임 (수락 후 설정됨) - 하위 호환성을 위해 유지
@override final  String? invitedNickname;
/// 초대를 수락한 사용자들 목록 (최대 2명) - Firebase에서만 사용
 final  List<InvitedUser> _acceptedUsers;
/// 초대를 수락한 사용자들 목록 (최대 3명) - Firebase에서만 사용
@override@JsonKey() List<InvitedUser> get acceptedUsers {
  if (_acceptedUsers is EqualUnmodifiableListView) return _acceptedUsers;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_acceptedUsers);
}

/// 초대 상태
@override@JsonKey() final  InvitationStatus status;
/// 초대 생성 시간
@override final  DateTime createdAt;
/// 초대 만료 시간 (생성 후 2시간)
@override final  DateTime expiresAt;
/// 초대 응답 시간 (수락/거절 시 설정됨)
@override final  DateTime? respondedAt;

/// Create a copy of EventInvitation
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EventInvitationCopyWith<_EventInvitation> get copyWith => __$EventInvitationCopyWithImpl<_EventInvitation>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EventInvitation&&(identical(other.id, id) || other.id == id)&&(identical(other.invitationCode, invitationCode) || other.invitationCode == invitationCode)&&(identical(other.eventId, eventId) || other.eventId == eventId)&&(identical(other.eventName, eventName) || other.eventName == eventName)&&(identical(other.ownerUserId, ownerUserId) || other.ownerUserId == ownerUserId)&&(identical(other.ownerNickname, ownerNickname) || other.ownerNickname == ownerNickname)&&(identical(other.invitedUserId, invitedUserId) || other.invitedUserId == invitedUserId)&&(identical(other.invitedNickname, invitedNickname) || other.invitedNickname == invitedNickname)&&const DeepCollectionEquality().equals(other._acceptedUsers, _acceptedUsers)&&(identical(other.status, status) || other.status == status)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt)&&(identical(other.respondedAt, respondedAt) || other.respondedAt == respondedAt));
}


@override
int get hashCode => Object.hash(runtimeType,id,invitationCode,eventId,eventName,ownerUserId,ownerNickname,invitedUserId,invitedNickname,const DeepCollectionEquality().hash(_acceptedUsers),status,createdAt,expiresAt,respondedAt);

@override
String toString() {
  return 'EventInvitation(id: $id, invitationCode: $invitationCode, eventId: $eventId, eventName: $eventName, ownerUserId: $ownerUserId, ownerNickname: $ownerNickname, invitedUserId: $invitedUserId, invitedNickname: $invitedNickname, acceptedUsers: $acceptedUsers, status: $status, createdAt: $createdAt, expiresAt: $expiresAt, respondedAt: $respondedAt)';
}


}

/// @nodoc
abstract mixin class _$EventInvitationCopyWith<$Res> implements $EventInvitationCopyWith<$Res> {
  factory _$EventInvitationCopyWith(_EventInvitation value, $Res Function(_EventInvitation) _then) = __$EventInvitationCopyWithImpl;
@override @useResult
$Res call({
 String id, String invitationCode, int eventId, String eventName, String ownerUserId, String ownerNickname, String? invitedUserId, String? invitedNickname, List<InvitedUser> acceptedUsers, InvitationStatus status, DateTime createdAt, DateTime expiresAt, DateTime? respondedAt
});




}
/// @nodoc
class __$EventInvitationCopyWithImpl<$Res>
    implements _$EventInvitationCopyWith<$Res> {
  __$EventInvitationCopyWithImpl(this._self, this._then);

  final _EventInvitation _self;
  final $Res Function(_EventInvitation) _then;

/// Create a copy of EventInvitation
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? invitationCode = null,Object? eventId = null,Object? eventName = null,Object? ownerUserId = null,Object? ownerNickname = null,Object? invitedUserId = freezed,Object? invitedNickname = freezed,Object? acceptedUsers = null,Object? status = null,Object? createdAt = null,Object? expiresAt = null,Object? respondedAt = freezed,}) {
  return _then(_EventInvitation(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,invitationCode: null == invitationCode ? _self.invitationCode : invitationCode // ignore: cast_nullable_to_non_nullable
as String,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,eventName: null == eventName ? _self.eventName : eventName // ignore: cast_nullable_to_non_nullable
as String,ownerUserId: null == ownerUserId ? _self.ownerUserId : ownerUserId // ignore: cast_nullable_to_non_nullable
as String,ownerNickname: null == ownerNickname ? _self.ownerNickname : ownerNickname // ignore: cast_nullable_to_non_nullable
as String,invitedUserId: freezed == invitedUserId ? _self.invitedUserId : invitedUserId // ignore: cast_nullable_to_non_nullable
as String?,invitedNickname: freezed == invitedNickname ? _self.invitedNickname : invitedNickname // ignore: cast_nullable_to_non_nullable
as String?,acceptedUsers: null == acceptedUsers ? _self._acceptedUsers : acceptedUsers // ignore: cast_nullable_to_non_nullable
as List<InvitedUser>,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as InvitationStatus,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,expiresAt: null == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime,respondedAt: freezed == respondedAt ? _self.respondedAt : respondedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
