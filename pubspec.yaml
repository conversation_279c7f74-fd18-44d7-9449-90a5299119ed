name: parabara
description: 바라 부스 매니저 - 동인 부스 관리앱
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.8+11

environment:
  sdk: ^3.8.1  # 현재 환경에 맞춰 유지
  flutter: ">=3.8.0"  # 현재 환경에 맞춰 조정

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  # --- Flutter 기본 ---
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # --- 상태관리(Riverpod) ---
  riverpod: ^2.6.1           # Riverpod 핵심 패키지 (2.6.1: 안정성과 호환성)
  flutter_riverpod: ^2.6.1   # Flutter용 Riverpod (2.6.1: 위와 동일)
  hooks_riverpod: ^2.6.1     # Hook 스타일 Riverpod (2.6.1: 위와 동일)
  riverpod_annotation: ^2.6.1 # 코드 생성용 어노테이션 (2.6.1: 위와 동일)

  # --- SQLite 데이터베이스 ---
  sqflite: ^2.4.2            # SQLite DB 연동 (2.4.2: 2025년 기준 최신 안정)
  path: ^1.9.1               # 파일 경로 처리 (DB 파일 경로 지정용, 최신 안정)
  path_provider: ^2.1.5      # OS별 경로 제공 (DB 저장 위치 지정, 최신 안정)

  # --- 로컬/웹 스토리지 ---
  shared_preferences: ^2.5.3 # 간단한 키-값 저장 (최신 안정)
  shared_preferences_web: ^2.4.2 # 웹용 shared_preferences

  # --- 라우팅 ---
  go_router: ^14.6.1 # 웹 라우팅용

  # --- 이미지/파일 처리 ---
  image_picker: ^1.1.2       # 갤러리/카메라 이미지 선택 (최신 안정)
  image: ^4.3.0              # 이미지 디코딩/처리 (excel 호환성 유지: archive ^3.6.1 공통)
  crop_your_image: ^2.0.0    # 이미지 크롭 기능 (최신 안정)
  file_picker: ^10.3.1       # 파일 선택(엑셀 등) (최신 안정)
  excel: ^4.0.6              # 엑셀 파일 읽기/쓰기 (최신 안정)
  pdf: ^3.11.3               # PDF 파일 생성 (최신 안정)
  printing: ^5.14.2          # PDF 인쇄/공유/미리보기 (최신 안정)
  cached_network_image: ^3.4.1 # 네트워크 이미지 캐싱 및 메모리 최적화 (성능 향상)
  permission_handler: ^12.0.1 # 권한 관리 (파일 저장용)

  # --- JSON/코드 생성 ---
  json_annotation: ^4.9.0    # JSON 직렬화 어노테이션 (최신 안정)

  # --- 성능 최적화 ---
  flutter_displaymode: ^0.6.0 # 고주사율 디스플레이 최적화 (120Hz+ 지원)
  visibility_detector: ^0.4.0+2 # 위젯 가시성 감지로 불필요한 렌더링 방지
  flutter_cache_manager: ^3.4.1 # 고급 캐시 관리 (이미지, 파일 캐싱 최적화)
  
  # --- 유틸리티/기타 ---
  cupertino_icons: ^1.0.8    # iOS 스타일 아이콘 (최신 안정)
  equatable: ^2.0.7          # 값 비교용 (최신 안정)
  collection: ^1.19.1        # 컬렉션 유틸 (최신 안정)
  logger: ^2.6.1             # 로그 출력 (최신 안정)
  crypto: ^3.0.6            # 암호화/해시 함수 (네트워크 최적화용)
  encrypt: ^5.0.3           # AES 암호화 패키지 (나이스페이 빌링용)
  intl: ^0.20.2              # 다국어/날짜포맷 (최신 안정)
  flutter_hooks: ^0.21.3     # Hook 스타일 위젯 (최신 안정)
  synchronized: ^3.4.0       # 동기화 유틸 (최신 안정)
  connectivity_plus: ^6.1.5  # 네트워크 상태 확인 (최신 안정)
  another_flushbar: ^1.12.30 # 커스텀 알림창 (최신 안정)
  mobile_scanner: ^7.0.1 # 바코드 스캔 패키지 (최신 안정)
  open_file: ^3.5.10       # 파일 자동 열기 (최신 안정)
  url_launcher: ^6.3.2 # 링크 열기 패키지 (최신 안정)
  mask_text_input_formatter: ^2.9.0 # 마스킹 텍스트 입력 포맷터 (최신 안정)
  firebase_core: ^4.0.0 # 파이어베이스 코어 패키지 (최신 안정)
  firebase_app_check: ^0.4.0 # 파이어베이스 앱 체크 패키지 (최신 안정)
  cloud_firestore: ^6.0.0 # 파이어베이스 파이어스토어 패키지 (최신 안정)
  firebase_auth: ^6.0.1 # 파이어베이스 인증 패키지 (최신 안정)
  cloud_functions: ^6.0.0 # 파이어베이스 Functions 호출 패키지 (최신 안정)

  syncfusion_flutter_datepicker: ^30.2.5 # 달력 패키지 (최신 안정)
  syncfusion_localizations: ^30.2.5 # 달력 패키지 (최신 안정)
  firebase_performance: ^0.11.0 # 파이어베이스 성능 측정 패키지 (최신 안정)
  firebase_crashlytics: ^5.0.0 # 파이어베이스 크래시 리포팅 패키지 (최신 안정)
  sign_in_button: ^4.0.1 # 로그인 버튼 패키지 (최신 안정)
  google_sign_in: ^7.1.1 # 구글 로그인 패키지 (최신 안정)

  firebase_analytics: ^12.0.0 # 파이어베이스 분석 패키지 (최신 안정)
  firebase_storage: ^13.0.0 # 파이어베이스 스토리지 패키지 (최신 안정)
  http: ^1.5.0 # HTTP 요청 패키지 (최신 안정)
  share_plus: ^11.1.0 # 공유 패키지 (최신 안정)

  # --- 디자인 ---
  flex_color_scheme: ^8.3.0 # 컬러 스킴 패키지 (최신 안정)
  flutter_staggered_grid_view: ^0.7.0 # 그리드 레이아웃 (MasonryGridView)
  shimmer: ^3.0.0
  lucide_icons: ^0.257.0 
  flutter_phoenix: ^1.1.1
  fl_chart: ^1.0.0
  syncfusion_flutter_charts: ^30.2.5
  flutter_slidable: ^4.0.0
  webview_flutter: ^4.13.0
  uuid: ^4.5.1
  flutter_recaptcha_v2_compat: ^1.0.3+3

dev_dependencies:
  # --- 테스트 ---
  flutter_test:
    sdk: flutter
  test: ^1.25.8               # Dart 테스트 프레임워크 (Flutter SDK 호환성 유지)
  mockito: ^5.5.0              # Mock 객체 생성 (최신 안정)
  sqflite_common_ffi: ^2.3.6   # 테스트용 SQLite FFI (최신 안정)
  sqflite_common_ffi_web: ^1.0.1+1 # 웹 테스트용 SQLite (최신 안정)

  # --- 코드 생성/분석 ---
  build_runner: ^2.6.0         # 코드 생성 자동화 (freezed 호환성)
  json_serializable: ^6.10.0   # JSON 코드 생성 (호환성 유지)
  analyzer: ^7.7.1             # 코드 분석기 (freezed 3.2.0 호환성)
  freezed_annotation: ^3.1.0   # freezed 코드 생성용 (호환성 맞춤)
  freezed: ^3.2.0              # 불변 상태 클래스 자동 생성 (성능 및 타입 안전성 향상)
  _fe_analyzer_shared: ^85.0.0 # analyzer 내부 의존성 (analyzer 7.7.1 호환)
  flutter_launcher_icons: ^0.14.4
  flutter_lints: ^6.0.0

flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/bara_app_icon.png"
  remove_alpha_ios: true


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/

  # 커스텀 폰트 설정
  fonts:
    - family: Pretendard
      fonts:
        - asset: assets/fonts/Pretendard-Light.otf
          weight: 300
        - asset: assets/fonts/Pretendard-Regular.otf
        - asset: assets/fonts/Pretendard-Medium.otf
          weight: 500
        - asset: assets/fonts/Pretendard-SemiBold.otf
          weight: 600
        - asset: assets/fonts/Pretendard-Bold.otf
          weight: 700
        - asset: assets/fonts/Pretendard-Black.otf
          weight: 900
    # 추가: 한글 PDF 품질 향상을 위한 NotoSansKR (TTF) - 실제 TTF 파일을 assets/fonts/ 아래에 배치해야 함
    - family: NotoSansKR
      fonts:
        - asset: assets/fonts/NotoSansKR-Regular.ttf
          weight: 400
        - asset: assets/fonts/NotoSansKR-Bold.ttf
          weight: 700

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package