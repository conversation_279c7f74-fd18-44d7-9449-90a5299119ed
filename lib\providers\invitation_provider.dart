/// 바라 부스 매니저 - 초대 Provider
///
/// 행사 초대 관련 상태 관리 및 비즈니스 로직을 처리하는 Provider입니다.
/// - 초대 생성, 수락, 거절
/// - 초대 목록 관리
/// - 초대 코드 검증
/// - Riverpod 기반 상태 관리
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/event_invitation.dart';
import '../models/invitation_status.dart';
import '../models/seller.dart';
import '../services/invitation_service.dart';
import '../repositories/event_invitation_repository.dart';
import '../repositories/seller_repository.dart';
import '../services/permission_service.dart';
import '../services/data_sync_service.dart';
import '../services/event_workspace_manager.dart';
import '../utils/logger_utils.dart';
import 'seller_provider.dart';
import 'permission_provider.dart';
import 'event_provider.dart';
import 'data_sync_provider.dart';

/// 초대 상태 클래스
class InvitationState {
  final List<EventInvitation> sentInvitations;
  final List<EventInvitation> receivedInvitations;
  final bool isLoading;
  final String? errorMessage;

  const InvitationState({
    this.sentInvitations = const [],
    this.receivedInvitations = const [],
    this.isLoading = false,
    this.errorMessage,
  });

  InvitationState copyWith({
    List<EventInvitation>? sentInvitations,
    List<EventInvitation>? receivedInvitations,
    bool? isLoading,
    String? errorMessage,
  }) {
    return InvitationState(
      sentInvitations: sentInvitations ?? this.sentInvitations,
      receivedInvitations: receivedInvitations ?? this.receivedInvitations,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }
}

/// 초대 Provider
class InvitationNotifier extends StateNotifier<InvitationState> {
  static const String _tag = 'InvitationNotifier';

  final InvitationService _invitationService;
  final EventInvitationRepository _repository;
  final SellerRepository _sellerRepository;
  final PermissionService _permissionService;
  final DataSyncService _dataSyncService;
  final Ref _ref;

  InvitationNotifier(
    this._invitationService,
    this._repository,
    this._sellerRepository,
    this._permissionService,
    this._dataSyncService,
    this._ref,
  ) : super(const InvitationState());

  /// 초대 생성
  /// 
  /// [eventId] 행사 ID
  /// [eventName] 행사명
  /// [ownerNickname] 소유자 닉네임
  /// 반환값: 생성된 초대 정보
  Future<EventInvitation> createInvitation({
    required int eventId,
    required String eventName,
    required String ownerNickname,
  }) async {
    LoggerUtils.methodStart('createInvitation', tag: _tag, data: {
      'eventId': eventId,
      'eventName': eventName,
      'ownerNickname': ownerNickname,
    });

    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final invitation = await _invitationService.createInvitation(
        eventId: eventId,
        eventName: eventName,
        ownerNickname: ownerNickname,
      );

      // 보낸 초대 목록에 추가
      final updatedSentInvitations = [...state.sentInvitations, invitation];
      state = state.copyWith(
        sentInvitations: updatedSentInvitations,
        isLoading: false,
      );

      LoggerUtils.logInfo('초대 생성 완료: ${invitation.invitationCode}', tag: _tag);
      return invitation;
    } catch (e) {
      LoggerUtils.logError('초대 생성 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  /// 초대 코드로 초대 정보 조회
  /// 
  /// [invitationCode] 초대 코드
  /// 반환값: 초대 정보 (없으면 null)
  Future<EventInvitation?> getInvitationByCode(String invitationCode) async {
    LoggerUtils.methodStart('getInvitationByCode', tag: _tag, data: {
      'invitationCode': invitationCode,
    });

    try {
      final invitation = await _invitationService.getInvitationByCode(invitationCode);
      LoggerUtils.logInfo('초대 코드 조회 완료', tag: _tag);
      return invitation;
    } catch (e) {
      LoggerUtils.logError('초대 코드 조회 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 초대 코드 검증
  ///
  /// [invitationCode] 초대 코드
  /// [userId] 검증하는 사용자 ID (중복 참여 확인용)
  /// 반환값: 검증 결과 메시지 (성공 시 null)
  Future<String?> validateInvitationCode(String invitationCode, {String? userId}) async {
    LoggerUtils.methodStart('validateInvitationCode', tag: _tag, data: {
      'invitationCode': invitationCode,
      'userId': userId,
    });

    try {
      final result = await _invitationService.validateInvitationCode(invitationCode, userId: userId);
      LoggerUtils.logInfo('초대 코드 검증 완료', tag: _tag);
      return result;
    } catch (e) {
      LoggerUtils.logError('초대 코드 검증 실패', tag: _tag, error: e);
      return '초대 코드 검증 중 오류가 발생했습니다';
    }
  }

  /// 초대 수락
  ///
  /// [invitationId] 초대 ID
  /// [userNickname] 수락하는 사용자 닉네임
  /// [currentInvitation] 현재 초대 정보 (권한 문제 해결을 위해 추가)
  /// 반환값: 업데이트된 초대 정보
  Future<EventInvitation> acceptInvitation(String invitationId, String userNickname, {EventInvitation? currentInvitation}) async {
    LoggerUtils.methodStart('acceptInvitation', tag: _tag, data: {
      'invitationId': invitationId,
      'userNickname': userNickname,
    });

    state = state.copyWith(isLoading: true, errorMessage: null);

    EventInvitation? updatedInvitation;
    final List<String> completedSteps = [];
    final List<String> failedSteps = [];

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('로그인이 필요합니다');
      }

      LoggerUtils.logInfo('🚀 초대 수락 프로세스 시작 - 사용자: $userNickname', tag: _tag);

      // 1. 초대 수락 처리
      try {
        LoggerUtils.logInfo('📝 1단계: 초대 수락 처리 시작', tag: _tag);
        updatedInvitation = await _repository.acceptInvitation(
          invitationId,
          user.uid,
          userNickname,
          currentInvitation: currentInvitation, // 권한 문제 해결을 위해 전달
        );
        completedSteps.add('초대 수락 처리');
        LoggerUtils.logInfo('✅ 1단계 완료: 초대 수락 처리 성공', tag: _tag);
      } catch (e) {
        failedSteps.add('초대 수락 처리: $e');
        LoggerUtils.logError('❌ 1단계 실패: 초대 수락 처리', tag: _tag, error: e);
        rethrow; // 이 단계는 필수이므로 실패 시 전체 프로세스 중단
      }

      // 2. 권한 부여 (먼저 권한을 부여해야 Firestore 접근 가능)
      try {
        LoggerUtils.logInfo('🔐 2단계: 권한 부여 시작', tag: _tag);
        await _permissionService.grantPermission(
          eventId: updatedInvitation.eventId,
          userId: user.uid,
          userNickname: userNickname,
          grantedByUserId: updatedInvitation.ownerUserId,
        );
        completedSteps.add('권한 부여');
        LoggerUtils.logInfo('✅ 2단계 완료: 권한 부여 성공', tag: _tag);
      } catch (e) {
        failedSteps.add('권한 부여: $e');
        LoggerUtils.logError('❌ 2단계 실패: 권한 부여 (계속 진행)', tag: _tag, error: e);
        // 권한 부여 실패해도 계속 진행
      }

      // 3. 초대받은 행사 데이터 동기화 (판매자 등록 전에 행사 데이터가 로컬에 있어야 함)
      try {
        LoggerUtils.logInfo('🔄 3단계: 행사 데이터 동기화 시작', tag: _tag);
        await _syncInvitedEventData(updatedInvitation.eventId, updatedInvitation.ownerUserId);
        completedSteps.add('행사 데이터 동기화');
        LoggerUtils.logInfo('✅ 3단계 완료: 행사 데이터 동기화 성공', tag: _tag);
      } catch (e) {
        failedSteps.add('행사 데이터 동기화: $e');
        LoggerUtils.logError('❌ 3단계 실패: 행사 데이터 동기화 (계속 진행)', tag: _tag, error: e);
        // 동기화 실패해도 계속 진행
      }

      // 4. 해당 행사에 판매자로 자동 등록 (행사 데이터가 로컬에 있은 후)
      try {
        LoggerUtils.logInfo('👤 4단계: 판매자 등록 시작', tag: _tag);
        await _createSellerForInvitedUser(updatedInvitation.eventId, userNickname);
        completedSteps.add('판매자 등록');
        LoggerUtils.logInfo('✅ 4단계 완료: 판매자 등록 성공', tag: _tag);
      } catch (e) {
        failedSteps.add('판매자 등록: $e');
        LoggerUtils.logError('❌ 4단계 실패: 판매자 등록 (계속 진행)', tag: _tag, error: e);
        // 판매자 등록 실패해도 계속 진행
      }

      // 5. 행사 목록 새로고침
      try {
        LoggerUtils.logInfo('📋 5단계: 행사 목록 새로고침 시작', tag: _tag);
        await _refreshEventList();
        completedSteps.add('행사 목록 새로고침');
        LoggerUtils.logInfo('✅ 5단계 완료: 행사 목록 새로고침 성공', tag: _tag);
      } catch (e) {
        failedSteps.add('행사 목록 새로고침: $e');
        LoggerUtils.logError('❌ 5단계 실패: 행사 목록 새로고침 (계속 진행)', tag: _tag, error: e);
        // 새로고침 실패해도 계속 진행
      }

      // 6. 워크스페이스 목록 새로고침 및 새 행사로 전환
      try {
        LoggerUtils.logInfo('🏢 6단계: 워크스페이스 새로고침 및 전환 시작', tag: _tag);
        await _refreshWorkspacesAndSwitchToInvitedEvent(updatedInvitation.eventId);
        completedSteps.add('워크스페이스 새로고침 및 전환');
        LoggerUtils.logInfo('✅ 6단계 완료: 워크스페이스 새로고침 및 전환 성공', tag: _tag);
      } catch (e) {
        failedSteps.add('워크스페이스 새로고침 및 전환: $e');
        LoggerUtils.logError('❌ 6단계 실패: 워크스페이스 새로고침 및 전환 (계속 진행)', tag: _tag, error: e);
        // 워크스페이스 전환 실패해도 계속 진행
      }

      // 받은 초대 목록 업데이트
      final updatedReceivedInvitations = state.receivedInvitations.map((invitation) {
        return invitation.id == invitationId ? updatedInvitation! : invitation;
      }).toList();

      state = state.copyWith(
        receivedInvitations: updatedReceivedInvitations,
        isLoading: false,
      );

      // 최종 결과 로깅
      LoggerUtils.logInfo('🎉 초대 수락 프로세스 완료!', tag: _tag);
      LoggerUtils.logInfo('✅ 성공한 단계 (${completedSteps.length}개): ${completedSteps.join(", ")}', tag: _tag);
      if (failedSteps.isNotEmpty) {
        LoggerUtils.logWarning('⚠️ 실패한 단계 (${failedSteps.length}개): ${failedSteps.join(", ")}', tag: _tag);
      }
      LoggerUtils.logInfo('초대 수락 완료: ${updatedInvitation.eventName}', tag: _tag);

      return updatedInvitation;
    } catch (e) {
      LoggerUtils.logError('💥 초대 수락 전체 프로세스 실패', tag: _tag, error: e);
      LoggerUtils.logInfo('✅ 성공한 단계: ${completedSteps.join(", ")}', tag: _tag);
      LoggerUtils.logError('❌ 실패한 단계: ${failedSteps.join(", ")}', tag: _tag);

      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  /// 초대받은 사용자를 판매자로 등록
  Future<void> _createSellerForInvitedUser(int eventId, String userNickname) async {
    try {
      LoggerUtils.logInfo('👤 판매자 등록 프로세스 시작 - 행사ID: $eventId, 닉네임: $userNickname', tag: _tag);

      // 해당 행사에 동일한 이름의 판매자가 있는지 확인
      LoggerUtils.logInfo('🔍 기존 판매자 목록 조회 중...', tag: _tag);
      final existingSellers = await _sellerRepository.getSellersByEventId(eventId);
      LoggerUtils.logInfo('📊 기존 판매자 수: ${existingSellers.length}개', tag: _tag);

      final existingSeller = existingSellers.where((s) => s.name == userNickname).firstOrNull;

      if (existingSeller == null) {
        LoggerUtils.logInfo('✨ 새 판매자 생성 중...', tag: _tag);
        // 새 판매자 생성 (isDefault: false, 초대받은 사용자이므로)
        final seller = Seller.create(
          name: userNickname,
          isDefault: false, // 초대받은 사용자는 기본 판매자가 아님
          eventId: eventId,
        );

        LoggerUtils.logInfo('💾 판매자 데이터베이스 저장 중...', tag: _tag);
        await _sellerRepository.insertSeller(seller);
        LoggerUtils.logInfo('✅ 초대받은 사용자 판매자 등록 완료: $userNickname (ID: ${seller.id})', tag: _tag);
      } else {
        LoggerUtils.logInfo('ℹ️ 동일한 이름의 판매자가 이미 존재: $userNickname (ID: ${existingSeller.id})', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('❌ 초대받은 사용자 판매자 등록 실패', tag: _tag, error: e);
      // 판매자 등록 실패해도 초대 수락은 유지
    }
  }

  /// 초대 거절
  /// 
  /// [invitationId] 초대 ID
  /// 반환값: 업데이트된 초대 정보
  Future<EventInvitation> rejectInvitation(String invitationId) async {
    LoggerUtils.methodStart('rejectInvitation', tag: _tag, data: {
      'invitationId': invitationId,
    });

    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('로그인이 필요합니다');
      }

      final updatedInvitation = await _repository.rejectInvitation(invitationId, user.uid);

      // 받은 초대 목록 업데이트
      final updatedReceivedInvitations = state.receivedInvitations.map((invitation) {
        return invitation.id == invitationId ? updatedInvitation : invitation;
      }).toList();

      state = state.copyWith(
        receivedInvitations: updatedReceivedInvitations,
        isLoading: false,
      );

      LoggerUtils.logInfo('초대 거절 완료', tag: _tag);
      return updatedInvitation;
    } catch (e) {
      LoggerUtils.logError('초대 거절 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  /// 보낸 초대 목록 로드
  /// 
  /// [eventId] 행사 ID (선택사항)
  Future<void> loadSentInvitations({int? eventId}) async {
    LoggerUtils.methodStart('loadSentInvitations', tag: _tag, data: {
      'eventId': eventId,
    });

    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('로그인이 필요합니다');
      }

      List<EventInvitation> invitations;
      if (eventId != null) {
        invitations = await _repository.getInvitationsByEvent(eventId, ownerUserId: user.uid);
      } else {
        // 모든 행사의 보낸 초대 조회 (추후 구현 필요)
        invitations = [];
      }

      state = state.copyWith(
        sentInvitations: invitations,
        isLoading: false,
      );

      LoggerUtils.logInfo('보낸 초대 목록 로드 완료: ${invitations.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('보낸 초대 목록 로드 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// 받은 초대 목록 로드
  /// 
  /// [status] 초대 상태 (선택사항)
  Future<void> loadReceivedInvitations({InvitationStatus? status}) async {
    LoggerUtils.methodStart('loadReceivedInvitations', tag: _tag, data: {
      'status': status?.value,
    });

    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('로그인이 필요합니다');
      }

      final invitations = await _repository.getInvitationsByUser(user.uid, status: status);

      state = state.copyWith(
        receivedInvitations: invitations,
        isLoading: false,
      );

      LoggerUtils.logInfo('받은 초대 목록 로드 완료: ${invitations.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('받은 초대 목록 로드 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// 초대 삭제
  /// 
  /// [invitationId] 초대 ID
  Future<void> deleteInvitation(String invitationId) async {
    LoggerUtils.methodStart('deleteInvitation', tag: _tag, data: {
      'invitationId': invitationId,
    });

    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      await _repository.deleteInvitation(invitationId);

      // 보낸 초대 목록에서 제거
      final updatedSentInvitations = state.sentInvitations
          .where((invitation) => invitation.id != invitationId)
          .toList();

      state = state.copyWith(
        sentInvitations: updatedSentInvitations,
        isLoading: false,
      );

      LoggerUtils.logInfo('초대 삭제 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('초대 삭제 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  /// 에러 메시지 클리어
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// 초대받은 행사 데이터 동기화
  Future<void> _syncInvitedEventData(int eventId, String ownerUserId) async {
    try {
      LoggerUtils.logInfo('초대받은 행사 데이터 동기화 시작: eventId=$eventId', tag: _tag);
      await _dataSyncService.syncInvitedEventData(eventId, ownerUserId);
      LoggerUtils.logInfo('초대받은 행사 데이터 동기화 완료: eventId=$eventId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('초대받은 행사 데이터 동기화 실패: eventId=$eventId', tag: _tag, error: e);
      // 동기화 실패해도 초대 수락은 완료된 상태이므로 에러를 던지지 않음
    }
  }

  /// 행사 목록 새로고침
  Future<void> _refreshEventList() async {
    try {
      LoggerUtils.logInfo('행사 목록 새로고침 시작', tag: _tag);
      await _ref.read(eventNotifierProvider.notifier).refresh();
      LoggerUtils.logInfo('행사 목록 새로고침 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('행사 목록 새로고침 실패', tag: _tag, error: e);
      // 새로고침 실패해도 초대 수락은 완료된 상태이므로 에러를 던지지 않음
    }
  }

  /// 워크스페이스 목록 새로고침 및 초대받은 행사로 전환
  Future<void> _refreshWorkspacesAndSwitchToInvitedEvent(int eventId) async {
    try {
      LoggerUtils.logInfo('워크스페이스 목록 새로고침 및 초대받은 행사로 전환 시작: eventId=$eventId', tag: _tag);

      // 워크스페이스 목록 새로고침
      await EventWorkspaceManager.instance.refreshWorkspaces();

      // 새로 추가된 행사를 워크스페이스 목록에서 찾기
      final workspaces = EventWorkspaceManager.instance.workspaces;
      final invitedWorkspace = workspaces.where((w) => w.id == eventId).firstOrNull;

      if (invitedWorkspace != null) {
        LoggerUtils.logInfo('초대받은 행사로 워크스페이스 전환: ${invitedWorkspace.name}', tag: _tag);
        await EventWorkspaceManager.instance.switchToWorkspace(invitedWorkspace);
      } else {
        LoggerUtils.logWarning('초대받은 행사를 워크스페이스 목록에서 찾을 수 없습니다: eventId=$eventId', tag: _tag);
      }

      LoggerUtils.logInfo('워크스페이스 목록 새로고침 및 초대받은 행사로 전환 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('워크스페이스 목록 새로고침 및 초대받은 행사로 전환 실패', tag: _tag, error: e);
      // 실패해도 초대 수락은 완료된 상태이므로 에러를 던지지 않음
    }
  }
}

/// 초대 서비스 Provider
final invitationServiceProvider = Provider<InvitationService>((ref) {
  return InvitationService();
});

/// 초대 Repository Provider
final eventInvitationRepositoryProvider = Provider<EventInvitationRepository>((ref) {
  return EventInvitationRepository();
});

/// 초대 Provider
final invitationNotifierProvider = StateNotifierProvider<InvitationNotifier, InvitationState>((ref) {
  final invitationService = ref.read(invitationServiceProvider);
  final repository = ref.read(eventInvitationRepositoryProvider);
  final sellerRepository = ref.read(sellerRepositoryProvider);
  final permissionService = ref.read(permissionServiceProvider);
  final dataSyncService = ref.read(dataSyncServiceProvider);
  return InvitationNotifier(invitationService, repository, sellerRepository, permissionService, dataSyncService, ref);
});

/// 특정 행사의 보낸 초대 목록 Provider
final eventSentInvitationsProvider = FutureProvider.family<List<EventInvitation>, int>((ref, eventId) async {
  final repository = ref.read(eventInvitationRepositoryProvider);
  final user = FirebaseAuth.instance.currentUser;
  if (user == null) return [];

  return await repository.getInvitationsByEvent(eventId, ownerUserId: user.uid);
});

/// 받은 초대 목록 Provider
final receivedInvitationsProvider = FutureProvider<List<EventInvitation>>((ref) async {
  final repository = ref.read(eventInvitationRepositoryProvider);
  final user = FirebaseAuth.instance.currentUser;
  if (user == null) return [];

  return await repository.getInvitationsByUser(user.uid, status: InvitationStatus.pending);
});
