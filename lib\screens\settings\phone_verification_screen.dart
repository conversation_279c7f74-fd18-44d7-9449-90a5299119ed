import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../utils/app_colors.dart';
import '../../utils/logger_utils.dart';

/// 전화번호 인증 화면
class PhoneVerificationScreen extends StatefulWidget {
  const PhoneVerificationScreen({super.key});

  @override
  State<PhoneVerificationScreen> createState() => _PhoneVerificationScreenState();
}

class _PhoneVerificationScreenState extends State<PhoneVerificationScreen>
    with WidgetsBindingObserver {
  // 웹뷰 관련
  WebViewController? _webViewController;
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeWebView();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 🔥 앱 생명주기에 따른 WebView 렌더링 제어
    switch (state) {
      case AppLifecycleState.resumed:
        _resumeWebView();
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        _pauseWebView();
        break;
      case AppLifecycleState.hidden:
        _pauseWebView();
        break;
    }
  }

  /// WebView 일시정지 (렌더링 중단)
  void _pauseWebView() {
    try {
      _webViewController?.runJavaScript('''
        // 모든 애니메이션과 타이머 중단
        document.querySelectorAll('*').forEach(el => {
          el.style.animationPlayState = 'paused';
          el.style.webkitAnimationPlayState = 'paused';
        });

        // 불필요한 렌더링 중단
        document.body.style.visibility = 'hidden';

        console.log('WebView 렌더링 일시정지');
      ''');
    } catch (e) {
      LoggerUtils.logError('WebView 일시정지 실패: $e');
    }
  }

  /// WebView 재개 (렌더링 재시작)
  void _resumeWebView() {
    try {
      _webViewController?.runJavaScript('''
        // 애니메이션 재개 (하지만 최소화)
        document.querySelectorAll('*').forEach(el => {
          el.style.animationPlayState = 'running';
          el.style.webkitAnimationPlayState = 'running';
        });

        // 렌더링 재개
        document.body.style.visibility = 'visible';

        console.log('WebView 렌더링 재개');
      ''');
    } catch (e) {
      LoggerUtils.logError('WebView 재개 실패: $e');
    }
  }

  @override
  void dispose() {
    // 🔥 WebView 완전 정리
    try {
      _webViewController?.runJavaScript('''
        // 모든 렌더링 중단
        document.body.style.display = 'none';
        document.documentElement.style.display = 'none';

        // JavaScript 정리
        if (window.recaptchaV3Token) {
          window.recaptchaV3Token = null;
        }
        if (window.currentUserUID) {
          window.currentUserUID = null;
        }

        // 모든 타이머와 애니메이션 중단
        const highestTimeoutId = setTimeout(";", 0);
        for (let i = 0; i < highestTimeoutId; i++) {
          clearTimeout(i);
        }

        const highestIntervalId = setInterval(";", 9999);
        for (let i = 0; i < highestIntervalId; i++) {
          clearInterval(i);
        }

        // 이벤트 리스너 정리
        document.removeEventListener('DOMContentLoaded', arguments.callee);

        console.log('WebView 완전 정리 완료');
      ''');
    } catch (e) {
      LoggerUtils.logError('WebView 정리 중 오류: $e');
    }

    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  /// 웹뷰 초기화
  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      // 🔥 WebView 성능 최적화 설정 추가
      ..setBackgroundColor(Colors.white)
      ..enableZoom(false)
      // 🔥 추가 성능 최적화 설정
      ..setUserAgent('Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36')
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            LoggerUtils.logInfo('전화번호 인증 웹뷰 로드 시작: $url');
          },
          onPageFinished: (String url) async {
            LoggerUtils.logInfo('전화번호 인증 웹뷰 로드 완료: $url');

            // 🔥 WebView 렌더링 최적화 JavaScript 실행 (더 강력한 최적화)
            try {
              await _webViewController!.runJavaScript('''
                // 강력한 렌더링 최적화
                document.documentElement.style.cssText = `
                  -webkit-transform: translateZ(0);
                  transform: translateZ(0);
                  -webkit-backface-visibility: hidden;
                  backface-visibility: hidden;
                  -webkit-perspective: 1000;
                  perspective: 1000;
                  will-change: auto;
                `;

                document.body.style.cssText = `
                  -webkit-transform: translateZ(0);
                  transform: translateZ(0);
                  -webkit-backface-visibility: hidden;
                  backface-visibility: hidden;
                  overflow-x: hidden;
                  -webkit-overflow-scrolling: touch;
                  -webkit-font-smoothing: antialiased;
                  -moz-osx-font-smoothing: grayscale;
                  text-rendering: optimizeSpeed;
                `;

                // 모든 애니메이션 비활성화
                const style = document.createElement('style');
                style.textContent = `
                  *, *::before, *::after {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                    scroll-behavior: auto !important;
                    -webkit-transform: translateZ(0);
                    transform: translateZ(0);
                  }
                `;
                document.head.appendChild(style);

                // 이미지 최적화
                const images = document.querySelectorAll('img');
                images.forEach(img => {
                  img.loading = 'lazy';
                  img.style.willChange = 'auto';
                });

                // reCAPTCHA 및 Firebase 인증 중 렌더링 최적화
                if (window.grecaptcha) {
                  window.grecaptcha.render = function() { return 'optimized'; };
                }

                // Firebase 인증 중 렌더링 최적화
                if (window.firebase) {
                  const originalAuth = window.firebase.auth;
                  window.firebase.auth = function() {
                    const auth = originalAuth.apply(this, arguments);
                    // 인증 중 렌더링 최적화
                    document.body.style.willChange = 'auto';
                    return auth;
                  };
                }

                console.log('강력한 WebView 성능 최적화 설정 완료');
              ''');
            } catch (e) {
              LoggerUtils.logError('WebView 최적화 스크립트 실행 실패: $e');
            }

            // 페이지 로드 완료 후 사용자 UID 전달
            final user = FirebaseAuth.instance.currentUser;
            if (user != null) {
              await _webViewController!.runJavaScript(
                'window.setUserInfo("${user.uid}");'
              );
              LoggerUtils.logInfo('웹뷰에 사용자 UID 전달 완료: ${user.uid}');
            }
          },
          onWebResourceError: (WebResourceError error) {
            LoggerUtils.logError('웹뷰 로드 오류: ${error.description}');
          },
          onHttpError: (HttpResponseError error) {
            LoggerUtils.logError('웹뷰 HTTP 오류: ${error.response?.statusCode}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'phoneVerificationSuccess',
        onMessageReceived: (JavaScriptMessage message) {
          LoggerUtils.logInfo('전화번호 인증 성공: ${message.message}');
          _handleVerificationSuccess(message.message);
        },
      )
      ..addJavaScriptChannel(
        'smsProcessing',
        onMessageReceived: (JavaScriptMessage message) {
          LoggerUtils.logInfo('SMS 처리 상태: ${message.message}');
          _handleSmsProcessing(message.message);
        },
      )
      ..loadRequest(Uri.parse('https://parabara-1a504.web.app/phone-verification.html?nocache=${DateTime.now().millisecondsSinceEpoch}'));
  }

  /// SMS 처리 상태 관리 (문자 발송 중 렌더링 최적화)
  void _handleSmsProcessing(String status) {
    LoggerUtils.logInfo('SMS 처리 상태 변경: $status');

    if (status == 'sending') {
      // 🔥 문자 발송 시작 - 렌더링 최적화 모드 활성화
      _webViewController?.runJavaScript('''
        // 문자 발송 중 극강 렌더링 최적화
        document.body.style.cssText += `
          pointer-events: none;
          user-select: none;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
        `;

        // 모든 애니메이션 완전 중단
        document.querySelectorAll('*').forEach(el => {
          el.style.animationPlayState = 'paused';
          el.style.webkitAnimationPlayState = 'paused';
          el.style.willChange = 'auto';
        });

        console.log('SMS 발송 중 - 렌더링 최적화 활성화');
      ''');
    } else if (status == 'completed' || status == 'error') {
      // 🔥 문자 발송 완료 - 렌더링 정상화
      _webViewController?.runJavaScript('''
        // 문자 발송 완료 - 렌더링 정상화
        document.body.style.cssText = document.body.style.cssText.replace(/pointer-events: none;|user-select: none;|-webkit-user-select: none;|-moz-user-select: none;|-ms-user-select: none;/g, '');

        console.log('SMS 발송 완료 - 렌더링 정상화');
      ''');
    }
  }

  /// 전화번호 인증 성공 처리
  void _handleVerificationSuccess(String message) {
    try {
      LoggerUtils.logInfo('전화번호 인증 메시지 수신: $message');

      // JSON 파싱
      final Map<String, dynamic> data = json.decode(message);
      final String? action = data['action'] as String?;
      final bool verified = data['verified'] as bool? ?? false;

      if (verified) {
        LoggerUtils.logInfo('전화번호 인증 완료: ${data['phoneNumber']}');

        // 웹뷰 자동 종료 요청이 있거나 인증이 완료된 경우
        if (action == 'close' || verified) {
          if (mounted) {
            Navigator.of(context).pop(true); // 성공 결과 반환

            // 성공 다이얼로그 표시
            _showSuccessDialog();
          }
        }
      }
    } catch (e) {
      LoggerUtils.logError('전화번호 인증 결과 처리 오류', error: e);
      // JSON 파싱 실패 시에도 성공으로 처리 (이전 방식 호환)
      if (mounted) {
        Navigator.of(context).pop(true);
        _showSuccessDialog();
      }
    }
  }

  /// 인증 성공 다이얼로그 표시
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            SizedBox(width: 12),
            Text('인증 완료'),
          ],
        ),
        content: const Text('전화번호 인증이 완료되었습니다!\n이제 구독 서비스를 이용하실 수 있습니다.'),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primarySeed,
              foregroundColor: Colors.white,
            ),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('전화번호 인증'),
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: Container(
          // 🔥 WebView 컨테이너 최적화 (크기 고정)
          width: double.infinity,
          height: double.infinity,
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
          child: RepaintBoundary(
            // 🔥 리페인트 경계 설정으로 렌더링 최적화
            child: SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: WebViewWidget(
                controller: _webViewController!,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
