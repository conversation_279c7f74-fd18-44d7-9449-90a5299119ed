import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:async';

import '../models/seller.dart';
import '../models/nickname.dart';
import '../repositories/seller_repository.dart';
import '../utils/logger_utils.dart';
import '../utils/provider_error_handler.dart';
import '../utils/network_status.dart';
import '../services/database_service.dart';
import 'base_state.dart';
import '../models/event_workspace.dart';
import '../models/subscription_plan.dart';
import 'unified_workspace_provider.dart';
import 'realtime_sync_provider.dart';
import 'subscription_provider.dart';
import '../services/realtime_sync_service_main.dart';
import 'data_sync_provider.dart';
import 'sales_log_provider.dart';
import 'nickname_provider.dart';
import 'revenue_goal_provider.dart';

// Custom Provider Exception
class CustomProviderException implements Exception {
  final String message;
  CustomProviderException(this.message);

  @override
  String toString() => message;
}

/// 판매자 상태를 관리하는 State 클래스입니다.
/// - 전체 판매자, 필터링/정렬 결과, 검색어, 로딩/에러/업데이트 상태 등 포함
/// - ProviderException, isCancelled 등 오류/취소/비동기 상태도 함께 관리
class SellerState extends BaseState {
  final List<Seller> sellers;
  final List<Seller> filteredSellers;
  final String searchQuery;
  final bool isUpdating;
  final Seller? defaultSeller;

  const SellerState({
    this.sellers = const [],
    this.filteredSellers = const [],
    this.searchQuery = '',
    this.isUpdating = false,
    this.defaultSeller,
    super.isLoading = false,
    super.errorMessage,
    super.errorCode,
    super.errorSeverity,
    super.errorDetails,
    super.isCancelled = false,
  });

  @override
  SellerState copyWithBase({
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
  }) {
    return copyWith(
      isLoading: isLoading,
      errorMessage: errorMessage,
      errorCode: errorCode,
      errorSeverity: errorSeverity,
      errorDetails: errorDetails,
      isCancelled: isCancelled,
    );
  }

  SellerState copyWith({
    List<Seller>? sellers,
    List<Seller>? filteredSellers,
    String? searchQuery,
    bool? isLoading,
    String? errorMessage,
    String? errorCode,
    bool? isUpdating,
    String? errorSeverity,
    Map<String, String>? errorDetails,
    bool? isCancelled,
    Seller? defaultSeller,
  }) {
    return SellerState(
      sellers: sellers ?? this.sellers,
      filteredSellers: filteredSellers ?? this.filteredSellers,
      searchQuery: searchQuery ?? this.searchQuery,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      isUpdating: isUpdating ?? this.isUpdating,
      errorSeverity: errorSeverity ?? this.errorSeverity,
      errorDetails: errorDetails ?? this.errorDetails,
      isCancelled: isCancelled ?? this.isCancelled,
      defaultSeller: defaultSeller ?? this.defaultSeller,
    );
  }

  @override
  List<Object?> get props => [
    ...super.props,
    sellers,
    filteredSellers,
    searchQuery,
    isUpdating,
  ];
}

final sellerRepositoryProvider = Provider<SellerRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return SellerRepository(database: databaseService);
});

class SellerNotifier extends StateNotifier<SellerState> {
  static const String _tag = 'SellerNotifier';
  final Ref ref;
  final bool autoInit;
  StreamSubscription<RealtimeDataChange>? _realtimeSubscription;

  // 무한 루프 방지를 위한 최근 추가한 판매자 캐시
  final Set<int> _recentlyAddedSellers = <int>{};

  SellerNotifier(this.ref, {this.autoInit = false}) : super(const SellerState()) {
    if (autoInit) {
      loadSellers();
    }
    _watchCurrentEvent();
    _setupRealtimeSync();
    _watchNicknameChanges();
  }

  /// 현재 행사 워크스페이스 변경 감지 및 자동 새로고침
  void _watchCurrentEvent() {
    ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) {
      if (previous?.id != next?.id) {
        LoggerUtils.logInfo('현재 행사 워크스페이스 변경 감지 - SellerNotifier 새로고침: ${previous?.name} -> ${next?.name}', tag: _tag);
        
        // 이벤트 전환 시 메모리 정리
        _clearAllDataForEventTransition();
        
        if (next != null) {
          loadSellers();
          _setupRealtimeSync(); // 새 워크스페이스에 대한 실시간 동기화 재설정
        } else {
          // 현재 행사 워크스페이스가 null이 되면 판매자 목록 클리어
          state = state.copyWith(
            sellers: [],
            filteredSellers: [],
            errorMessage: '행사 워크스페이스를 선택해주세요. 왼쪽 상단 메뉴에서 행사 워크스페이스를 선택하거나 생성할 수 있습니다.',
          );
        }
      }
    });
  }

  /// 닉네임 변경 감지 및 기본 판매자 이름 자동 업데이트
  void _watchNicknameChanges() {
    ref.listen<Nickname?>(nicknameProvider, (previous, next) {
      if (previous?.name != next?.name && next?.name != null) {
        LoggerUtils.logInfo('닉네임 변경 감지: ${previous?.name} → ${next?.name}', tag: _tag);
        _updateDefaultSellerName(previous?.name, next!.name);
      }
    });
  }

  /// 실시간 동기화 설정
  void _setupRealtimeSync() {
    try {
      // 기존 구독 해제
      _realtimeSubscription?.cancel();

      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 워크스페이스가 없어 실시간 동기화를 설정할 수 없습니다', tag: _tag);
        return;
      }

      // 프로 플랜만 실시간 동기화 지원
      final subscriptionState = ref.read(subscriptionNotifierProvider);
      final isProUser = subscriptionState.when(
        data: (planType) => planType == SubscriptionPlanType.pro,
        loading: () => false,
        error: (_, __) => false,
      );
      if (!isProUser) {
        LoggerUtils.logInfo('프로 플랜이 아니므로 판매자 실시간 동기화를 건너뜁니다', tag: _tag);
        return;
      }

      // 실시간 동기화 서비스 가져오기
      final realtimeService = ref.read(realtimeSyncServiceProvider);

      // 중복 구독 방지: 이미 다른 곳에서 구독이 시작되었다고 가정
      LoggerUtils.logInfo('실시간 데이터 스트림 구독 시작 (중복 구독 방지)', tag: _tag);

      // 데이터 변경 리스너 설정
      _realtimeSubscription = realtimeService.dataChanges.listen((change) {
        _handleRealtimeDataChange(change);
      });

      LoggerUtils.logInfo('SellerNotifier 실시간 동기화 리스너 설정 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('SellerNotifier 실시간 동기화 설정 실패', tag: _tag, error: e);
    }
  }

  /// 실시간 데이터 변경 처리 - 개별 변경사항만 처리 (Local-First)
  void _handleRealtimeDataChange(RealtimeDataChange change) {
    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      
      // 현재 워크스페이스의 판매자 변경인지 확인
      if (currentWorkspace?.id == change.eventId && change.collectionName == 'sellers') {
        LoggerUtils.logInfo('판매자 실시간 변경 감지: ${change.changeType.name} - ${change.documentId}', tag: _tag);
        
        // 개별 변경사항만 처리 (전체 재로드 없이)
        _processSingleSellerChange(change);
      }
    } catch (e) {
      LoggerUtils.logError('실시간 데이터 변경 처리 실패', tag: _tag, error: e);
    }
  }

  /// 개별 판매자 변경사항 처리
  Future<void> _processSingleSellerChange(RealtimeDataChange change) async {
    try {
      final sellerId = int.tryParse(change.documentId);
      if (sellerId == null) return;

      switch (change.changeType) {
        case RealtimeChangeType.added:
        case RealtimeChangeType.modified:
          // 자기가 최근에 추가한 판매자는 무시 (무한 루프 방지)
          if (_recentlyAddedSellers.contains(sellerId)) {
            LoggerUtils.logDebug('최근 추가한 판매자 무시: ID $sellerId', tag: _tag);
            return;
          }

          if (change.data != null) {
            final sellerData = Map<String, dynamic>.from(change.data!);
            sellerData['id'] = sellerId;
            final seller = Seller.fromJson(sellerData);
            await _syncSellerToLocalSafe(seller);
            await _refreshSellerState();
          }
          break;
          
        case RealtimeChangeType.removed:
          // 안전한 삭제 처리 - 실제로 삭제되었는지 확인
          await _removeSellerFromLocalSafely(sellerId, change.eventId);
          await _refreshSellerState();
          break;
      }
    } catch (e) {
      LoggerUtils.logError('개별 판매자 변경 처리 실패: ${change.documentId}', tag: _tag, error: e);
    }
  }

  /// 판매자를 로컬에 안전하게 동기화
  Future<void> _syncSellerToLocalSafe(Seller seller) async {
    try {
      final repository = ref.read(sellerRepositoryProvider);
      
      // 기존 판매자 확인
      final existingSeller = await repository.getSellerById(seller.id!);
      
      if (existingSeller != null) {
        // 업데이트
        await repository.updateSeller(seller);
        LoggerUtils.logDebug('판매자 로컬 업데이트 완료: ${seller.name}', tag: _tag);
      } else {
        // 새로 추가
        await repository.insertSeller(seller);
        LoggerUtils.logDebug('판매자 로컬 추가 완료: ${seller.name}', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('판매자 로컬 동기화 실패: ${seller.name}', tag: _tag, error: e);
    }
  }

  /// 판매자를 로컬에서 안전하게 제거 (안전성 강화 버전)
  Future<void> _removeSellerFromLocalSafely(int sellerId, int eventId) async {
    try {
      // 1. 네트워크 연결 상태 확인
      if (!NetworkStatusUtil.isOnline) {
        LoggerUtils.logWarning('네트워크 연결 없음 - 판매자 삭제 보류: $sellerId', tag: _tag);
        return;
      }

      // 2. 로컬에 해당 데이터가 실제로 존재하는지 확인
      final repository = ref.read(sellerRepositoryProvider);
      final existingSeller = await repository.getSellerById(sellerId);

      if (existingSeller == null) {
        LoggerUtils.logInfo('판매자가 로컬에 존재하지 않음 - 삭제 건너뛰기: $sellerId', tag: _tag);
        return;
      }

      // 3. Firebase에서 실제로 삭제되었는지 재확인 (선택적)
      final user = FirebaseAuth.instance.currentUser;
      if (user != null && eventId > 0) {
        try {
          final doc = await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .collection('events')
              .doc(eventId.toString())
              .collection('sellers')
              .doc(sellerId.toString())
              .get();

          if (doc.exists) {
            LoggerUtils.logWarning('Firebase에 여전히 존재하는 판매자 - 삭제하지 않음: $sellerId', tag: _tag);
            return;
          }
        } catch (e) {
          LoggerUtils.logWarning('Firebase 재확인 실패 - 로컬 삭제 진행: $sellerId', tag: _tag, error: e);
          // Firebase 확인 실패 시에도 로컬 삭제는 진행
        }
      }

      // 4. 로컬에서 삭제 수행
      await repository.deleteSeller(sellerId);
      LoggerUtils.logInfo('판매자 안전 삭제 완료: $sellerId', tag: _tag);

    } catch (e) {
      LoggerUtils.logError('판매자 안전 삭제 실패: $sellerId', tag: _tag, error: e);
      rethrow;
    }
  }



  /// 판매자 상태 새로고침 (로컬 DB에서 다시 로드) - 무한 루프 방지
  Future<void> _refreshSellerState() async {
    try {
      // 이미 로딩 중이면 건너뛰기 (무한 루프 방지)
      if (state.isLoading) {
        LoggerUtils.logDebug('이미 로딩 중이므로 상태 새로고침 생략', tag: _tag);
        return;
      }
      
      await loadSellers(showLoading: false);
    } catch (e) {
      LoggerUtils.logError('판매자 상태 새로고침 실패', tag: _tag, error: e);
    }
  }

  /// 이벤트 전환 시 모든 데이터 정리 - 메모리 누수 방지
  void _clearAllDataForEventTransition() {
    try {
      LoggerUtils.logInfo('이벤트 전환 - SellerNotifier 데이터 정리 시작', tag: _tag);

      // 실시간 구독 해제
      _realtimeSubscription?.cancel();
      _realtimeSubscription = null;

      // 상태 완전 초기화
      state = const SellerState();

      LoggerUtils.logInfo('이벤트 전환 - SellerNotifier 데이터 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('SellerNotifier 데이터 정리 중 오류: $e', tag: _tag);
    }
  }

  /// SellerNotifier dispose 처리
  @override
  void dispose() {
    _realtimeSubscription?.cancel(); // 실시간 동기화 구독 해제
    super.dispose();
  }

  Future<void> loadSellers({bool showLoading = true}) async {
    // 🔥 실시간 구독 상태 확인 - 캐시가 아닌 실시간 상태 사용
    final subscriptionService = ref.read(subscriptionServiceProvider);
    final currentPlanType = await subscriptionService.getCurrentPlanType();
    final isProPlan = currentPlanType == SubscriptionPlanType.pro;

    if (showLoading) {
      state = state.copyWith(isLoading: true);
    }
    try {
      // 현재 선택된 행사 워크스페이스 확인
      EventWorkspace? currentWorkspace = ref.read(currentWorkspaceProvider);

      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 행사 워크스페이스가 없습니다', tag: 'SellerNotifier');
        state = state.copyWith(
          sellers: [],
          filteredSellers: [],
          defaultSeller: null,
          isLoading: false,
          errorMessage: '행사 워크스페이스를 선택해주세요',
        );
        return;
      }

      final repository = ref.read(sellerRepositoryProvider);
      final sellers = await repository.getSellersByEventId(currentWorkspace.id);
      final defaultSeller = await repository.getDefaultSellerByEventId(currentWorkspace.id);

      // 프로 플랜이 아닌 경우 기본 판매자만 표시하고 여러 판매자 관리 기능 제한
      List<Seller> displaySellers = sellers;
      String? errorMessage;

      if (!isProPlan) {
        // 기본 판매자만 표시 (있는 경우)
        if (defaultSeller != null) {
          displaySellers = [defaultSeller];
          errorMessage = '여러 판매자 관리는 프로 플랜에서만 이용 가능합니다.';
        } else {
          // 기본 판매자가 없으면 자동 생성 시도
          final createdSeller = await _createDefaultSellerIfNeeded(currentWorkspace.id);
          if (createdSeller != null) {
            displaySellers = [createdSeller];
            errorMessage = '여러 판매자 관리는 프로 플랜에서만 이용 가능합니다.';
            LoggerUtils.logInfo('기본 판매자 자동 생성 완료: ${createdSeller.name}', tag: _tag);
          } else {
            displaySellers = [];
            errorMessage = '기본 판매자를 생성할 수 없습니다. 닉네임을 설정해주세요.';
            LoggerUtils.logWarning('기본 판매자 자동 생성 실패', tag: _tag);
          }
        }
        LoggerUtils.logInfo('프리 플랜 - 기본 판매자만 표시: ${displaySellers.length}개', tag: _tag);
      }

      final filteredSellers = _applyFiltersAndSort(displaySellers, searchQuery: '');
      state = state.copyWith(
        sellers: displaySellers,
        filteredSellers: filteredSellers,
        defaultSeller: defaultSeller,
        isLoading: false,
        errorMessage: errorMessage,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      // 기존 sellers 데이터는 유지
    }
  }

  /// 대표 판매자 설정
  Future<void> setDefaultSeller(int sellerId) async {
    try {
      await ProviderErrorHandler.executeWithErrorHandling<void, SellerState>(
        operation: () async {
          final repository = ref.read(sellerRepositoryProvider);
          await repository.setDefaultSeller(sellerId);
        },
        notifier: this,
        errorCode: 'SEL_SET_DEFAULT_ERROR',
        tag: _tag,
        operationName: '대표 판매자 설정',
        setLoadingState: false,
      );
      // 성공 시 판매자 목록 새로고침
      await loadSellers();
    } catch (e) {
      // 에러 상태 유지
    }
  }

  /// 대표 판매자 해제
  Future<void> unsetDefaultSeller(int sellerId) async {
    try {
      await ProviderErrorHandler.executeWithErrorHandling<void, SellerState>(
        operation: () async {
          final repository = ref.read(sellerRepositoryProvider);
          await repository.unsetDefaultSeller(sellerId);
        },
        notifier: this,
        errorCode: 'SEL_UNSET_DEFAULT_ERROR',
        tag: _tag,
        operationName: '대표 판매자 해제',
        setLoadingState: false,
      );
      // 성공 시 판매자 목록 새로고침
      await loadSellers();
    } catch (e) {
      // 에러 상태 유지
    }
  }

  /// 대표 판매자 여부 확인
  Future<bool> isDefaultSeller(int sellerId) async {
    try {
      final repository = ref.read(sellerRepositoryProvider);
      return await repository.isDefaultSeller(sellerId);
    } catch (e) {
      return false;
    }
  }

  List<Seller> _applyFiltersAndSort(List<Seller> sellers, {String? searchQuery}) {
    if (sellers.isEmpty) return [];
    var filtered = List<Seller>.from(sellers);
    final query = (searchQuery ?? '').trim().toLowerCase();
    if (query.isNotEmpty) {
      filtered = filtered.where((seller) {
        final name = seller.name.toLowerCase();
        final id = seller.id.toString();
        return name.contains(query) || id.contains(query);
      }).toList();
    }
    return filtered;
  }

  Future<void> searchSellers(String query) async {
    final currentState = state;
    final trimmedQuery = query.trim();
    final filteredSellers = _applyFiltersAndSort(currentState.sellers, searchQuery: trimmedQuery);
    state = currentState.copyWith(
      filteredSellers: filteredSellers,
      searchQuery: trimmedQuery,
    );
  }

  /// 판매자 추가 (로컬 DB + Firebase 동기화 포함)
  Future<void> addSeller(Seller seller) async {
    state = state.copyWith(isUpdating: true);
    bool success = false;
    try {
      // 현재 선택된 행사 워크스페이스 확인
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) {
        LoggerUtils.logWarning('현재 선택된 행사 워크스페이스가 없습니다', tag: _tag);
        state = state.copyWith(
          isUpdating: false,
          errorMessage: '행사 워크스페이스를 선택해주세요',
        );
        return;
      }

      // 판매자에 현재 워크스페이스 ID 설정
      final sellerWithEventId = seller.copyWith(eventId: currentWorkspace.id);

      int? sellerId;
      await ProviderErrorHandler.executeWithErrorHandling<void, SellerState>(
        operation: () async {
          final repository = ref.read(sellerRepositoryProvider);
          sellerId = await repository.insertSeller(sellerWithEventId);
        },
        notifier: this,
        errorCode: 'SEL_ADD_ERROR',
        tag: _tag,
        operationName: '판매자 추가',
        setLoadingState: false,
      );

      // 최근 추가한 판매자로 캐시 (무한 루프 방지용)
      if (sellerId != null) {
        _recentlyAddedSellers.add(sellerId!);
        // 5초 후 캐시에서 제거
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyAddedSellers.remove(sellerId!);
        });
      }

      // 실시간 동기화: 새 판매자를 Firebase에 즉시 업로드
      try {
        final sellerWithId = sellerWithEventId.copyWith(id: sellerId);
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSingleSeller(sellerWithId);
        LoggerUtils.logInfo('새 판매자 실시간 동기화 업로드 완료: ${sellerWithId.name}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('새 판매자 실시간 동기화 업로드 실패: ${sellerWithEventId.name}', tag: _tag, error: e);
        // 실시간 동기화 실패해도 로컬 추가는 유지
      }

      success = true;
    } catch (e) {
      // 에러 상태 유지
    } finally {
      state = state.copyWith(isUpdating: false);
      if (success) {
        await loadSellers();
      }
    }
  }



  /// 판매자 수정 (로컬 DB + Firebase 동기화 포함)
  Future<void> updateSeller(Seller seller) async {
    bool success = false;
    try {
      await ProviderErrorHandler.executeWithErrorHandling<void, SellerState>(
        operation: () async {
          final repository = ref.read(sellerRepositoryProvider);
          await repository.updateSeller(seller);
        },
        notifier: this,
        errorCode: 'SEL_UPDATE_ERROR',
        tag: _tag,
        operationName: '판매자 수정',
        setLoadingState: false,
      );

      // 실시간 동기화: 업데이트된 판매자를 Firebase에 즉시 업로드
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSingleSeller(seller);
        LoggerUtils.logInfo('판매자 업데이트 실시간 동기화 업로드 완료: ${seller.name}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('판매자 업데이트 실시간 동기화 업로드 실패: ${seller.name}', tag: _tag, error: e);
        // 실시간 동기화 실패해도 로컬 업데이트는 유지
      }

      // 관련 Provider들에게 판매자 업데이트 알림
      _notifyRelatedProviders(seller);

      success = true;
    } catch (e) {
      // 에러 상태 유지
    } finally {
      if (success) {
        await loadSellers();
      }
    }
  }

  /// 판매자의 특정 필드만 업데이트 (선택적 업데이트)
  Future<void> updateSellerFields(int sellerId, Map<String, dynamic> fields) async {
    try {
      LoggerUtils.methodStart('updateSellerFields', tag: _tag, data: {'id': sellerId, 'fields': fields.keys.toList()});

      // 현재 판매자 조회
      final currentSeller = state.sellers.firstWhere((s) => s.id == sellerId);

      // 필드 업데이트를 위한 copyWith 호출
      Seller updatedSeller = currentSeller;

      // 각 필드별로 업데이트 적용
      if (fields.containsKey('name')) {
        updatedSeller = updatedSeller.copyWith(name: fields['name'] as String);
      }

      if (fields.containsKey('isDefault')) {
        updatedSeller = updatedSeller.copyWith(isDefault: fields['isDefault'] as bool);
      }

      // 로컬 DB 업데이트
      final repository = ref.read(sellerRepositoryProvider);
      await repository.updateSeller(updatedSeller);

      // 상태 업데이트
      final updatedSellers = state.sellers.map((s) {
        return s.id == updatedSeller.id ? updatedSeller : s;
      }).toList();

      state = state.copyWith(sellers: updatedSellers);

      // 실시간 동기화: 변경된 필드만 Firebase에 업로드
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSellerFields(sellerId, fields, eventId: updatedSeller.eventId);
        LoggerUtils.logInfo('판매자 필드 업데이트 실시간 동기화 업로드 완료: ${fields.keys.join(', ')}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('판매자 필드 업데이트 실시간 동기화 업로드 실패', tag: _tag, error: e);
        // 실시간 동기화 실패해도 로컬 업데이트는 유지
      }

      // 관련 Provider들에게 판매자 업데이트 알림
      _notifyRelatedProviders(updatedSeller);

      LoggerUtils.methodEnd('updateSellerFields', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '판매자 필드 업데이트 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
        data: {'id': sellerId, 'fields': fields.keys.toList()},
      );
      rethrow;
    }
  }

  /// 관련 Provider들에게 판매자 업데이트 알림
  void _notifyRelatedProviders(Seller updatedSeller) {
    try {
      // 백그라운드에서 관련 Provider들 갱신 (UI 블로킹 방지)
      Future.microtask(() async {
        try {
          // 판매 기록 Provider 갱신 (판매자명 변경 시 필요)
          ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();

          // 목표 수익 Provider 갱신 (판매자별 목표 수익이 있는 경우)
          ref.read(revenueGoalNotifierProvider.notifier).loadGoals(showLoading: false);

          LoggerUtils.logInfo('판매자 관련 Provider들 갱신 완료: ${updatedSeller.name}', tag: _tag);
        } catch (e) {
          LoggerUtils.logError('판매자 관련 Provider 갱신 실패', tag: _tag, error: e);
        }
      });
    } catch (e) {
      LoggerUtils.logError('판매자 관련 Provider 알림 실패', tag: _tag, error: e);
    }
  }

  Future<void> deleteSeller(int id) async {
    bool success = false;
    Seller? sellerToDelete;
    
    try {
      // 삭제할 판매자 정보 미리 저장 (실시간 동기화용)
      sellerToDelete = state.sellers.firstWhere((s) => s.id == id);
      
      await ProviderErrorHandler.executeWithErrorHandling<void, SellerState>(
        operation: () async {
          final repository = ref.read(sellerRepositoryProvider);
          await repository.deleteSeller(id);
        },
        notifier: this,
        errorCode: 'SEL_DELETE_ERROR',
        tag: _tag,
        operationName: '판매자 삭제',
        setLoadingState: false,
      );

      // 실시간 동기화: 삭제된 판매자를 Firebase에서 즉시 삭제
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.deleteSingleSeller(sellerToDelete);
        LoggerUtils.logInfo('판매자 삭제 실시간 동기화 완료: ${sellerToDelete.name}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('판매자 삭제 실시간 동기화 실패: ${sellerToDelete.name}', tag: _tag, error: e);
        // 실시간 동기화 실패해도 로컬 삭제는 유지
      }

      success = true;
    } catch (e) {
      // 에러 상태 유지
    } finally {
      if (success) {
        await loadSellers();
      }
    }
  }

  /// 기본 판매자가 없을 때 자동 생성
  Future<Seller?> _createDefaultSellerIfNeeded(int eventId) async {
    try {
      LoggerUtils.logInfo('기본 판매자 자동 생성 시도: eventId=$eventId', tag: _tag);

      // 닉네임 정보 가져오기
      final nickname = ref.read(nicknameProvider);
      if (nickname == null) {
        LoggerUtils.logInfo('닉네임이 없어 기본 판매자 자동 생성을 건너뜁니다', tag: _tag);
        return null;
      }

      // 기본 판매자 생성
      final defaultSeller = Seller.create(
        name: nickname.name,
        isDefault: true,
        eventId: eventId,
      );

      final repository = ref.read(sellerRepositoryProvider);
      final sellerId = await repository.insertSeller(defaultSeller);

      if (sellerId > 0) {
        final createdSeller = defaultSeller.copyWith(id: sellerId);
        LoggerUtils.logInfo('기본 판매자 자동 생성 완료: ${nickname.name} (ID: $sellerId)', tag: _tag);
        return createdSeller;
      }

      return null;
    } catch (e) {
      LoggerUtils.logError('기본 판매자 자동 생성 실패', tag: _tag, error: e);
      return null;
    }
  }

  /// 닉네임 변경 시 기본 판매자 이름 업데이트
  Future<void> _updateDefaultSellerName(String? oldName, String newName) async {
    try {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace == null) return;

      LoggerUtils.logInfo('기본 판매자 이름 업데이트 시작: $oldName → $newName', tag: _tag);

      // 현재 워크스페이스의 기본 판매자를 직접 찾기 (이름 비교 없이)
      final repository = ref.read(sellerRepositoryProvider);
      final defaultSeller = await repository.getDefaultSellerByEventId(currentWorkspace.id);

      if (defaultSeller != null) {
        LoggerUtils.logInfo('기본 판매자 발견: ${defaultSeller.name} (ID: ${defaultSeller.id})', tag: _tag);

        // 기본 판매자 이름 업데이트
        final updatedSeller = defaultSeller.copyWith(name: newName);
        await repository.updateSeller(updatedSeller);

        // 상태에서 해당 판매자만 업데이트 (전체 새로고침 방지)
        final updatedSellers = state.sellers.map((seller) {
          if (seller.id == defaultSeller.id) {
            return updatedSeller;
          }
          return seller;
        }).toList();

        // defaultSeller도 함께 업데이트
        state = state.copyWith(
          sellers: updatedSellers,
          defaultSeller: updatedSeller,
        );

        LoggerUtils.logInfo('기본 판매자 이름 업데이트 완료: ${defaultSeller.name} → $newName', tag: _tag);
      } else {
        LoggerUtils.logInfo('현재 워크스페이스에 기본 판매자가 없습니다. 자동 생성을 시도합니다.', tag: _tag);

        // 기본 판매자가 없으면 자동 생성
        final createdSeller = await _createDefaultSellerIfNeeded(currentWorkspace.id);
        if (createdSeller != null) {
          LoggerUtils.logInfo('기본 판매자 자동 생성 및 이름 설정 완료: $newName', tag: _tag);
          // 상태 새로고침
          await loadSellers(showLoading: false);
        }
      }
    } catch (e) {
      LoggerUtils.logError('기본 판매자 이름 업데이트 실패', tag: _tag, error: e);
    }
  }


}

final sellerNotifierProvider = StateNotifierProvider<SellerNotifier, SellerState>((ref) {
  return SellerNotifier(ref);
});

// 하단의 @riverpod 함수형 Provider들도 일반 Provider로 변환 필요
final sellersProvider = Provider<List<Seller>>((ref) {
  return ref.watch(sellerNotifierProvider).sellers;
});



final filteredSellersProvider = Provider<List<Seller>>((ref) {
  return ref.watch(sellerNotifierProvider).filteredSellers;
});
final sellerIsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(sellerNotifierProvider).isLoading;
});
final sellerErrorMessageProvider = Provider<String?>((ref) {
  return ref.watch(sellerNotifierProvider).errorMessage;
});
final sellerErrorCodeProvider = Provider<String?>((ref) {
  return ref.watch(sellerNotifierProvider).errorCode;
});

/// 대표 판매자 Provider
final defaultSellerProvider = Provider<Seller?>((ref) {
  return ref.watch(sellerNotifierProvider).defaultSeller;
});

/// 대표 판매자 이름 Provider
final defaultSellerNameProvider = Provider<String>((ref) {
  final defaultSeller = ref.watch(sellerNotifierProvider).defaultSeller;
  return defaultSeller?.name ?? '';
});

/// Seller 데이터 동기화 관리자
class SellerDataSyncManager {
  static const String _tag = 'SellerDataSyncManager';
  
  /// 모든 Seller 관련 Provider를 일관되게 갱신
  static Future<void> syncAllSellerData(WidgetRef ref) async {
    LoggerUtils.logInfo('Seller 데이터 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Seller Notifier 갱신
      await ref.read(sellerNotifierProvider.notifier).loadSellers();
      
      LoggerUtils.logInfo('Seller 데이터 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Seller 데이터 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Seller 추가 후 동기화
  static Future<void> syncAfterAddSeller(WidgetRef ref, Seller seller) async {
    LoggerUtils.logInfo('Seller 추가 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Seller Notifier 추가
      await ref.read(sellerNotifierProvider.notifier).addSeller(seller);
      
      LoggerUtils.logInfo('Seller 추가 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Seller 추가 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Seller 수정 후 동기화
  static Future<void> syncAfterUpdateSeller(WidgetRef ref, Seller seller) async {
    LoggerUtils.logInfo('Seller 수정 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Seller Notifier 수정
      await ref.read(sellerNotifierProvider.notifier).updateSeller(seller);
      
      LoggerUtils.logInfo('Seller 수정 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Seller 수정 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// Seller 삭제 후 동기화
  static Future<void> syncAfterDeleteSeller(WidgetRef ref, int id) async {
    LoggerUtils.logInfo('Seller 삭제 후 동기화 시작', tag: _tag);
    
    try {
      // 단순화된 Seller Notifier 삭제
      await ref.read(sellerNotifierProvider.notifier).deleteSeller(id);
      
      LoggerUtils.logInfo('Seller 삭제 후 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Seller 삭제 후 동기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}

